import '../models/subscription_model.dart';

class SubscriptionConstants {
  static const Map<SubscriptionPlan, Map<String, dynamic>> planConfigs = {
    SubscriptionPlan.free: {
      'id': 'free',
      'name': 'Free Plan',
      'price': 0.0,
      'chatCredits': 25,
      'voiceCredits': 5,
      'durationDays': 7,
      'description': 'Perfect for trying out the app',
      'features': [
        '25 chat messages',
        '5 voice messages',
        '7-day access',
        'Breathing Excercise',
      ],
      'popular': false,
      'color': 0xFF9E9E9E,
      'available': true,
    },
    SubscriptionPlan.basic: {
      'id': 'basic',
      'name': 'Basic Plan',
      'price': 15.0,
      'chatCredits': 13000,
      'voiceCredits': 80,
      'durationDays': 30,
      'description': 'Great for regular users',
      'features': [
        '13,000 chat messages',
        '80 voice messages',
        'Chat history',
        '30-day access',
        'Breathing Excercise',
      ],
      'popular': true,
      'color': 0xFF2196F3,
      'available': true,
      'lemonsqueezyProductId': 'YOUR_BASIC_PLAN_ID',
    },
    SubscriptionPlan.premium: {
      'id': 'premium',
      'name': 'Premium Plan',
      'price': 30.0,
      'chatCredits': 20000,
      'voiceCredits': 300,
      'durationDays': 30,
      'description': 'Best value for power users',
      'features': [
        '20,000 chat messages',
        '300 voice messages',
        'Chat History',
        '30-days access',
        'Breathing Excercise',
        
      ],
      'popular': false,
      'color': 0xFFFF9800,
      'available': true,
      'lemonsqueezyProductId': 'YOUR_PREMIUM_PLAN_ID',
    },
  };

  static const Map<SubscriptionPlan, Map<String, int>> creditLimits = {
    SubscriptionPlan.free: {'chat': 25, 'voice': 5},
    SubscriptionPlan.basic: {'chat': 13000, 'voice': 80},
    SubscriptionPlan.premium: {'chat': 20000, 'voice': 300},
  };

  static const Map<SubscriptionPlan, int> planDurations = {
    SubscriptionPlan.free: 7,
    SubscriptionPlan.basic: 30,
    SubscriptionPlan.premium: 30,
  };

  static const Map<SubscriptionPlan, double> planPrices = {
    SubscriptionPlan.free: 0.0,
    SubscriptionPlan.basic: 15.0,
    SubscriptionPlan.premium: 30.0,
  };

  static Map<String, dynamic> getPlanConfig(SubscriptionPlan plan) {
    return planConfigs[plan] ?? planConfigs[SubscriptionPlan.free]!;
  }

  static int getChatLimit(SubscriptionPlan plan) {
    return creditLimits[plan]?['chat'] ?? 0;
  }

  static int getVoiceLimit(SubscriptionPlan plan) {
    return creditLimits[plan]?['voice'] ?? 0;
  }

  static int getPlanDuration(SubscriptionPlan plan) {
    return planDurations[plan] ?? 7;
  }

  static double getPlanPrice(SubscriptionPlan plan) {
    return planPrices[plan] ?? 0.0;
  }

  static String getPlanName(SubscriptionPlan plan) {
    return getPlanConfig(plan)['name'] ?? 'Unknown Plan';
  }

  static List<String> getPlanFeatures(SubscriptionPlan plan) {
    return List<String>.from(getPlanConfig(plan)['features'] ?? []);
  }

  static bool isPlanPopular(SubscriptionPlan plan) {
    return getPlanConfig(plan)['popular'] ?? false;
  }

  static int getPlanColor(SubscriptionPlan plan) {
    return getPlanConfig(plan)['color'] ?? 0xFF9E9E9E;
  }

  static bool isPlanAvailable(SubscriptionPlan plan) {
    return getPlanConfig(plan)['available'] ?? false;
  }

  static String? getLemonSqueezyProductId(SubscriptionPlan plan) {
    return getPlanConfig(plan)['lemonsqueezyProductId'];
  }

  static bool canUpgradeTo(SubscriptionPlan currentPlan, SubscriptionPlan targetPlan) {
    final currentIndex = SubscriptionPlan.values.indexOf(currentPlan);
    final targetIndex = SubscriptionPlan.values.indexOf(targetPlan);
    return targetIndex > currentIndex;
  }

  static bool canDowngradeTo(SubscriptionPlan currentPlan, SubscriptionPlan targetPlan) {
    final currentIndex = SubscriptionPlan.values.indexOf(currentPlan);
    final targetIndex = SubscriptionPlan.values.indexOf(targetPlan);
    return targetIndex < currentIndex;
  }

  static List<SubscriptionPlan> getUpgradeOptions(SubscriptionPlan currentPlan) {
    return SubscriptionPlan.values
        .where((plan) => canUpgradeTo(currentPlan, plan))
        .toList();
  }

  static const double warningThreshold = 0.8;
  static const double criticalThreshold = 0.95;

  static bool isNearLimit(int used, int limit) {
    if (limit == 0) return false;
    return (used / limit) >= warningThreshold;
  }

  static bool isCriticalLimit(int used, int limit) {
    if (limit == 0) return false;
    return (used / limit) >= criticalThreshold;
  }

  static const String noCreditsMessage = 'You have run out of credits. Please upgrade your plan to continue.';
  static const String expiredSubscriptionMessage = 'Your subscription has expired. Please renew to continue using the app.';
  static const String upgradeRequiredMessage = 'Upgrade your plan to access this feature.';
  
  static const String subscriptionUpgradeSuccess = 'Successfully upgraded your subscription!';
  static const String creditsRefreshed = 'Your credits have been refreshed for the new billing cycle.';

  static const String lemonsqueezyStoreId = 'your_store_id';
  static const String lemonsqueezyBaseUrl = 'https://api.lemonsqueezy.com/v1';

  static const String lemonsqueezyWebhookUrl = 'https://your-domain.com/webhook';
  
  static const int maxConversationHistory = 100;
  static const int maxMessageHistory = 1000;
  static const Duration sessionTimeout = Duration(hours: 24);
  
  static const bool enableStripe = false;
  static const bool enablePayPal = false;
  static const bool enableAnalytics = true;
  static const bool enableErrorReporting = true;
}
