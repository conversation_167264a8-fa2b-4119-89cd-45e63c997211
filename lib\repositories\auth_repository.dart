import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flight_fear_wellness_app/models/user_model.dart';
import 'package:flight_fear_wellness_app/services/firebase_service.dart';

// Purpose: Repository layer for authentication operations with error handling
class AuthRepository {
  final FirebaseService firebaseService;

  AuthRepository({required this.firebaseService});

  // Purpose: Handle user registration with comprehensive error handling
  Future<Either<String, UserModel>> signUp({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      final response = await firebaseService.signUp(
        email: email,
        password: password,
        name: name,
      );

      if (response.success && response.data != null) {
        return Right(response.data!);
      }

      if (response.error != null && 
          (response.error!.contains('PigeonUserDetails') || 
           response.error!.contains('List<Object?>'))) {
        
        print('Attempting alternative signup method...');
        
        final alternativeResponse = await firebaseService.signUpAlternative(
          email: email,
          password: password,
          name: name,
        );

        if (alternativeResponse.success && alternativeResponse.data != null) {
          return Right(alternativeResponse.data!);
        }
        
        return Left(alternativeResponse.error ?? 'Alternative signup failed');
      }

      return Left(response.error ?? 'Registration failed');
      
    } on FirebaseAuthException catch (e) {
      return Left(_parseFirebaseAuthError(e));
    } catch (e) {
      print('Unexpected error in AuthRepository.signUp: $e');
      return Left('Registration failed: ${e.toString()}');
    }
  }

  Future<Either<String, UserModel>> signUpDirect({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      UserCredential? userCredential;
      User? firebaseUser;
      
      try {
        userCredential = await FirebaseAuth.instance.createUserWithEmailAndPassword(
          email: email.trim().toLowerCase(),
          password: password,
        );
        firebaseUser = userCredential.user;
      } catch (e) {
        if (e.toString().contains('PigeonUserDetails') || 
            e.toString().contains('List<Object?>')) {
          
          await Future.delayed(const Duration(seconds: 1));
          firebaseUser = FirebaseAuth.instance.currentUser;
          
          if (firebaseUser == null) {
            return Left('User creation failed due to authentication error');
          }
        } else {
          rethrow;
        }
      }

      if (firebaseUser == null) {
        return Left('User creation failed');
      }

      final userModel = UserModel(
        id: firebaseUser.uid,
        email: email.trim().toLowerCase(),
        name: name.trim(),
        createdAt: DateTime.now(),
        lastActive: DateTime.now(),
      );

      final now = DateTime.now();
      final userData = {
        ...userModel.toMap(),
        'subscription': {
          'plan': 'free',
          'status': 'active',
          'startDate': Timestamp.fromDate(now),
          'endDate': Timestamp.fromDate(now.add(const Duration(days: 7))),
          'autoRenew': true,
          'trialUsed': false,
          'trialEndsAt': Timestamp.fromDate(now.add(const Duration(days: 7))),
          'paymentPlatform': null,
          'subscriptionId': null,
          'customerId': null,
        },
        'credits': {
          'chat': {
            'limit': 25,
            'used': 0,
            'remaining': 25,
          },
          'voice': {
            'limit': 5,
            'used': 0,
            'remaining': 5,
          },
          'resetDate': Timestamp.fromDate(now.add(const Duration(days: 7))),
        },
        'usage': {
          'totalChatPrompts': 0,
          'totalVoicePrompts': 0,
          'lastChatAt': null,
          'lastVoiceAt': null,
        },
        'payment': {
          'lemonsqueezy': {
            'customerId': null,
            'subscriptionId': null,
          },
          'stripe': {
            'customerId': null,
            'subscriptionId': null,
          },
          'paypal': {
            'customerId': null,
            'subscriptionId': null,
          },
        },
      };

      await FirebaseFirestore.instance
          .collection('users')
          .doc(firebaseUser.uid)
          .set(userData);

      return Right(userModel);

    } on FirebaseAuthException catch (e) {
      return Left(_parseFirebaseAuthError(e));
    } catch (e) {
      print('Direct signup error: $e');
      return Left('Registration failed: ${e.toString()}');
    }
  }

  String _parseFirebaseAuthError(FirebaseAuthException e) {
    switch (e.code) {
      case 'email-already-in-use':
        return 'This email is already registered';
      case 'invalid-email':
        return 'Please enter a valid email';
      case 'weak-password':
        return 'Password should be at least 6 characters';
      case 'user-not-found':
        return 'No account found with this email address';
      case 'wrong-password':
        return 'Incorrect password. Please try again';
      case 'user-disabled':
        return 'This account has been disabled';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later';
      case 'operation-not-allowed':
        return 'This sign-in method is not enabled';
      case 'network-request-failed':
        return 'Network error. Please check your connection';
      default:
        return 'Authentication failed: ${e.message ?? 'Unknown error'}';
    }
  }

  // Purpose: Handle user login with error handling and validation
  Future<Either<String, UserModel>> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final response = await firebaseService.signIn(
        email: email,
        password: password,
      );

      if (!response.success) {
        return Left(response.error ?? 'Login failed');
      }
      
      if (response.data == null) {
        return Left('User data not found');
      }
      
      return Right(response.data!);
    } on FirebaseAuthException catch (e) {
      return Left(_parseFirebaseAuthError(e));
    } catch (e) {
      return Left('Login failed: ${e.toString()}');
    }
  }

  Future<Either<String, Unit>> signOut() async {
    try {
      final response = await firebaseService.signOut();
      if (!response.success) {
        return Left(response.error ?? 'Sign out failed');
      }
      return const Right(unit);
    } catch (e) {
      return Left('Sign out failed: ${e.toString()}');
    }
  }

  Future<Either<String, UserModel>> getCurrentUser() async {
    try {
      final response = await firebaseService.getCurrentUserData();
      if (!response.success) {
        return Left(response.error ?? 'User not authenticated');
      }
      
      if (response.data == null) {
        return Left('User data not found');
      }
      
      return Right(response.data!);
    } catch (e) {
      return Left('Failed to get user: ${e.toString()}');
    }
  }


  Future<Either<String, bool>> resetPassword({required String email}) async {
    try {
      final response = await firebaseService.resetPassword(email: email);

      if (response.success) {
        return const Right(true);
      }

      return Left(response.error ?? 'Failed to send password reset email');
    } catch (e) {
      return Left('Password reset failed: ${e.toString()}');
    }
  }


  bool isUserSessionValid() {
    return firebaseService.isSessionValid();
  }

  Future<bool> refreshUserSession() async {
    return await firebaseService.refreshUserSession();
  }

  Future<Either<String, bool>> signOutUser() async {
    try {
      final response = await firebaseService.signOutUser();

      if (response.success) {
        return const Right(true);
      }

      return Left(response.error ?? 'Sign out failed');
    } catch (e) {
      return Left('Sign out failed: ${e.toString()}');
    }
  }

  bool get isSignedIn => FirebaseAuth.instance.currentUser != null;

  String? get currentUserId => FirebaseAuth.instance.currentUser?.uid;

  Stream<User?> get authStateChanges => FirebaseAuth.instance.authStateChanges();
}