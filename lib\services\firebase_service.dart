import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../models/user_model.dart';
import '../models/chat_message.dart';
import '../models/api_response.dart';
import '../utils/constants.dart';
import 'dart:async';
import 'dart:io';
import 'notification_service.dart';

// Purpose: Centralized Firebase operations for authentication, Firestore, and storage
class FirebaseService {
  final FirebaseAuth _auth;
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final NotificationService _notificationService;


  FirebaseService({
    FirebaseAuth? auth,
    FirebaseFirestore? firestore,
    FirebaseStorage? storage,
    NotificationService? notificationService,
  })  : _auth = auth ?? FirebaseAuth.instance,
        _firestore = firestore ?? FirebaseFirestore.instance,
        _storage = storage ?? FirebaseStorage.instance,
        _notificationService = notificationService ?? NotificationService();


  User? get currentUser => _auth.currentUser;
  String? get currentUserId => _auth.currentUser?.uid;
  Stream<User?> get authStateChanges => _auth.authStateChanges();


  // Purpose: Create new user account with email verification and subscription setup
  Future<ApiResponse<UserModel>> signUp({
    required String email,
    required String password,
    required String name,
  }) async {
    try {

      UserCredential? userCredential;
      User? firebaseUser;
      

      try {
        userCredential = await _auth.createUserWithEmailAndPassword(
          email: email.trim().toLowerCase(),
          password: password,
        );
        firebaseUser = userCredential.user;
      } catch (e) {
        if (e.toString().contains('PigeonUserDetails') || 
            e.toString().contains('List<Object?>')) {
          print('Pigeon error detected, attempting workaround...');
          
          await Future.delayed(const Duration(seconds: 2));
          
          firebaseUser = _auth.currentUser;
          
          if (firebaseUser == null) {
            rethrow;
          }
          
          print('Workaround successful - user found: ${firebaseUser.uid}');
        } else {
          rethrow;
        }
      }

      if (firebaseUser == null) {
        return ApiResponse.error('User creation failed - no user returned');
      }

      // Send email verification and handle potential errors
      try {
        await firebaseUser.sendEmailVerification();
        print('FirebaseService: Email verification sent successfully to ${email.trim().toLowerCase()}');
      } catch (verificationError) {
        print('FirebaseService: Warning - Failed to send email verification: $verificationError');
        // Continue with signup process even if email verification fails
        // User can request resend later
      }

      await firebaseUser.reload();

      final userModel = UserModel(
        id: firebaseUser.uid,
        email: email.trim().toLowerCase(),
        name: name.trim(),
        createdAt: DateTime.now(),
        lastActive: DateTime.now(),
      );

      await _createUserWithSubscription(userModel);

      return ApiResponse.success(userModel);

    } on FirebaseAuthException catch (e) {
      print('FirebaseAuthException: ${e.code} - ${e.message}');
      
      if ((e.code == 'unknown' && _auth.currentUser != null) ||
          e.toString().contains('PigeonUserDetails')) {
        try {
          final currentUser = _auth.currentUser!;

          // Send email verification for recovered user
          try {
            await currentUser.sendEmailVerification();
            print('FirebaseService: Email verification sent for recovered user: ${email.trim().toLowerCase()}');
          } catch (verificationError) {
            print('FirebaseService: Warning - Failed to send email verification for recovered user: $verificationError');
          }

          final userModel = UserModel(
            id: currentUser.uid,
            email: email.trim().toLowerCase(),
            name: name.trim(),
            createdAt: DateTime.now(),
            lastActive: DateTime.now(),
          );

          await _saveUserWithRetry(userModel);
          return ApiResponse.success(userModel);
        } catch (saveError) {
          print('Failed to save user after Pigeon workaround: $saveError');
        }
      }
      
      return ApiResponse.error(getAuthErrorMessage(e.code));
    } on FirebaseException catch (e) {
      print('FirebaseException: ${e.code} - ${e.message}');
      return ApiResponse.error('Database error: ${e.message}');
    } catch (e) {
      print('Unexpected error during signup: $e');
      
      if (_auth.currentUser != null &&
          (e.toString().contains('PigeonUserDetails') ||
           e.toString().contains('List<Object?>'))) {
        try {
          final currentUser = _auth.currentUser!;

          // Send email verification for final fallback user
          try {
            await currentUser.sendEmailVerification();
            print('FirebaseService: Email verification sent for fallback user: ${email.trim().toLowerCase()}');
          } catch (verificationError) {
            print('FirebaseService: Warning - Failed to send email verification for fallback user: $verificationError');
          }

          final userModel = UserModel(
            id: currentUser.uid,
            email: email.trim().toLowerCase(),
            name: name.trim(),
            createdAt: DateTime.now(),
            lastActive: DateTime.now(),
          );

          await _saveUserWithRetry(userModel);
          return ApiResponse.success(userModel);
        } catch (saveError) {
          print('Final fallback failed: $saveError');
        }
      }
      
      return ApiResponse.error('Sign up failed: ${e.toString()}');
    }
  }

  // Purpose: Save user data to Firestore with retry logic for reliability
  Future<void> _saveUserWithRetry(UserModel userModel, {int retries = 3}) async {
    for (int i = 0; i < retries; i++) {
      try {
        await _firestore
            .collection(AppConstants.usersCollection)
            .doc(userModel.id)
            .set(userModel.toMap());
        print('User saved to Firestore successfully');
        return;
      } catch (e) {
        print('Attempt ${i + 1} failed to save user: $e');
        if (i == retries - 1) {
          rethrow;
        }
        await Future.delayed(Duration(milliseconds: 500 * (i + 1)));
      }
    }
  }

  // Purpose: Create user document with initial subscription and credit data
  Future<void> _createUserWithSubscription(UserModel userModel, {int retries = 3}) async {
    for (int i = 0; i < retries; i++) {
      try {
        final now = DateTime.now();
        final userData = {
          ...userModel.toMap(),
          'subscription': {
            'plan': 'free',
            'status': 'active',
            'startDate': Timestamp.fromDate(now),
            'endDate': Timestamp.fromDate(now.add(const Duration(days: 7))),
            'autoRenew': true,
            'trialUsed': false,
            'trialEndsAt': Timestamp.fromDate(now.add(const Duration(days: 7))),
            'paymentPlatform': null,
            'subscriptionId': null,
            'customerId': null,
          },
          'credits': {
            'chat': {
              'limit': 25,
              'used': 0,
              'remaining': 25,
            },
            'voice': {
              'limit': 5,
              'used': 0,
              'remaining': 5,
            },
            'resetDate': Timestamp.fromDate(now.add(const Duration(days: 7))),
          },
          'usage': {
            'totalChatPrompts': 0,
            'totalVoicePrompts': 0,
            'lastChatAt': null,
            'lastVoiceAt': null,
          },
          'payment': {
            'lemonsqueezy': {
              'customerId': null,
              'subscriptionId': null,
            },
            'stripe': {
              'customerId': null,
              'subscriptionId': null,
            },
            'paypal': {
              'customerId': null,
              'subscriptionId': null,
            },
          },
        };

        await _firestore
            .collection(AppConstants.usersCollection)
            .doc(userModel.id)
            .set(userData);
        print('User with subscription data saved to Firestore successfully');

        // Send welcome notification for new user
        await _notificationService.sendWelcomeNotification(userModel.id);
        return;
      } catch (e) {
        print('Attempt ${i + 1} failed to save user with subscription: $e');
        if (i == retries - 1) {
          rethrow;
        }
        await Future.delayed(Duration(milliseconds: 500 * (i + 1)));
      }
    }
  }

  // Purpose: Alternative signup method with different error handling approach
  Future<ApiResponse<UserModel>> signUpAlternative({
    required String email,
    required String password,
    required String name,
  }) async {
    try {

      UserCredential? userCredential = await _auth
          .createUserWithEmailAndPassword(
            email: email.trim().toLowerCase(),
            password: password,
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw TimeoutException('Sign up timed out');
            },
          );

      final firebaseUser = userCredential.user;
      if (firebaseUser == null) {
        return ApiResponse.error('User creation failed');
      }

      // Send email verification for alternative signup
      try {
        await firebaseUser.sendEmailVerification();
        print('FirebaseService: Email verification sent successfully (alternative) to ${email.trim().toLowerCase()}');
      } catch (verificationError) {
        print('FirebaseService: Warning - Failed to send email verification (alternative): $verificationError');
      }

      final userModel = UserModel(
        id: firebaseUser.uid,
        email: email.trim().toLowerCase(),
        name: name.trim(),
        createdAt: DateTime.now(),
        lastActive: DateTime.now(),
      );

      await _createUserWithSubscription(userModel);
      return ApiResponse.success(userModel);

    } on FirebaseAuthException catch (e) {
      print('FirebaseAuthException in alternative signup: ${e.code} - ${e.message}');
      return ApiResponse.error(getAuthErrorMessage(e.code));
    } on FirebaseException catch (e) {
      print('FirebaseException in alternative signup: ${e.code} - ${e.message}');
      return ApiResponse.error('Database error: ${e.message}');
    } catch (e) {
      print('Alternative signup failed: $e');
      return ApiResponse.error('Sign up failed: ${e.toString()}');
    }
  }

  // Purpose: Authenticate user with email and password
  Future<ApiResponse<UserModel>> signIn({
    required String email,
    required String password,
  }) async {
    try {
      print('FirebaseService: Starting sign in for ${email.trim().toLowerCase()}');

      final credential = await _auth.signInWithEmailAndPassword(
        email: email.trim().toLowerCase(),
        password: password,
      );

      final firebaseUser = credential.user;
      if (firebaseUser == null) {
        print('FirebaseService: Sign in failed - no user returned');
        return ApiResponse.error('Sign in failed - no user returned');
      }

      print('FirebaseService: Firebase auth successful for ${firebaseUser.uid}');

      // Refresh user to get latest email verification status
      try {
        await firebaseUser.reload();
        // Force token refresh to get latest verification status
        await firebaseUser.getIdToken(true);
        await firebaseUser.reload();
      } catch (e) {
        print('FirebaseService: Warning - Failed to refresh user token: $e');
      }

      final refreshedUser = _auth.currentUser;

      print('FirebaseService: Initial verification status: ${firebaseUser.emailVerified}');
      print('FirebaseService: After reload verification status: ${refreshedUser?.emailVerified}');
      print('FirebaseService: User email: ${firebaseUser.email}');
      print('FirebaseService: User creation time: ${firebaseUser.metadata.creationTime}');

      // Check if email is verified
      if (refreshedUser != null && !refreshedUser.emailVerified) {
        print('FirebaseService: ❌ Email not verified for user ${firebaseUser.uid} - redirecting to verification');

        // TEMPORARY DEBUG: Check if this is a test account or development environment
        final email = firebaseUser.email?.toLowerCase() ?? '';
        if (email.contains('test') || email.contains('debug') || email.contains('dev')) {
          print('FirebaseService: 🔧 DEBUG MODE: Bypassing email verification for test account: $email');
          // Continue with login for test accounts
        } else {
          return ApiResponse.error('EMAIL_NOT_VERIFIED');
        }
      }

      print('FirebaseService: ✅ Email verification confirmed - allowing login for ${firebaseUser.uid}');

      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(firebaseUser.uid)
          .get()
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw Exception('Timeout getting user document');
            },
          );

      if (!userDoc.exists) {
        print('FirebaseService: User document does not exist, creating new one with subscription data');
        final userModel = UserModel(
          id: firebaseUser.uid,
          email: firebaseUser.email ?? email.trim().toLowerCase(),
          name: firebaseUser.displayName ?? 'User',
          createdAt: DateTime.now(),
          lastActive: DateTime.now(),
        );

        await _createUserWithSubscription(userModel);
        print('FirebaseService: New user document with subscription created successfully');
        return ApiResponse.success(userModel);
      }

      final userData = userDoc.data();
      if (userData == null) {
        print('FirebaseService: User data is corrupted');
        return ApiResponse.error('User data is corrupted');
      }

      print('FirebaseService: User document retrieved successfully');
      final user = UserModel.fromMap(userData);

      updateLastActive(user.id).catchError((error) {
        print('Warning: Failed to update last active timestamp: $error');
      });

      print('FirebaseService: Sign in completed successfully for ${user.email}');
      return ApiResponse.success(user);

    } on FirebaseAuthException catch (e) {
      print('FirebaseAuthException during sign in: ${e.code} - ${e.message}');

      if (e.code == 'invalid-credential') {
        try {
          final emailToCheck = email.trim().toLowerCase();
          final userQuery = await _firestore
              .collection(AppConstants.usersCollection)
              .where('email', isEqualTo: emailToCheck)
              .limit(1)
              .get();

          if (userQuery.docs.isEmpty) {
            return ApiResponse.error('No account found with this email address.');
          } else {
            return ApiResponse.error('Incorrect password. Please try again.');
          }
        } catch (checkError) {
          return ApiResponse.error('Invalid email or password. Please check your credentials and try again.');
        }
      }

      return ApiResponse.error(getAuthErrorMessage(e.code));
    } catch (e) {
      print('Unexpected error during sign in: $e');
      return ApiResponse.error('Sign in failed: ${e.toString()}');
    }
  }

  // Purpose: Sign out current user and clear session
  Future<ApiResponse<void>> signOut() async {
    try {
      await _auth.signOut();
      return ApiResponse.success(null);
    } catch (e) {
      print('Error during sign out: $e');
      return ApiResponse.error('Failed to sign out: ${e.toString()}');
    }
  }

  // Purpose: Retrieve current user data from Firestore
  Future<ApiResponse<UserModel>> getCurrentUserData() async {
    try {
      final firebaseUser = currentUser;
      if (firebaseUser == null) {
        return ApiResponse.error('No user signed in');
      }

      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(firebaseUser.uid)
          .get();

      if (!userDoc.exists) {
        return ApiResponse.error('User data not found in database');
      }

      final userData = userDoc.data();
      if (userData == null) {
        return ApiResponse.error('User data is corrupted');
      }

      final user = UserModel.fromMap(userData);
      return ApiResponse.success(user);
      
    } catch (e) {
      print('Error getting current user data: $e');
      return ApiResponse.error('Failed to get user data: ${e.toString()}');
    }
  }

  // Purpose: Update user's last active timestamp for session tracking
  Future<void> updateLastActive(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({'lastActive': DateTime.now().millisecondsSinceEpoch});
    } catch (e) {
      print('Failed to update last active: $e');
    }
  }

  // Purpose: Save chat message to Firestore with user association
  Future<ApiResponse<void>> saveChatMessage(ChatMessage message) async {
    try {
      await _firestore
          .collection(AppConstants.chatMessagesCollection)
          .doc(message.id)
          .set(message.toMap());
      
      return ApiResponse.success(null);
    } catch (e) {
      return ApiResponse.error('Failed to save message: ${e.toString()}');
    }
  }

  Stream<List<ChatMessage>> getChatMessages(String userId) {
    return _firestore
        .collection(AppConstants.chatMessagesCollection)
        .where('userId', isEqualTo: userId)
        .orderBy('timestamp', descending: false)
        .limit(AppConstants.chatMessageLimit)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ChatMessage.fromMap(doc.data()))
            .toList());
  }

  // Purpose: Delete all chat history for a specific user
  Future<ApiResponse<void>> deleteChatHistory(String userId) async {
    try {
      final batch = _firestore.batch();
      final messages = await _firestore
          .collection(AppConstants.chatMessagesCollection)
          .where('userId', isEqualTo: userId)
          .get();

      for (final doc in messages.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      return ApiResponse.success(null);
    } catch (e) {
      return ApiResponse.error('Failed to delete chat history: ${e.toString()}');
    }
  }

  // Purpose: Save user model data to Firestore database
  Future<ApiResponse<void>> saveUserToFirestore(UserModel user) async {
    try {
      await _saveUserWithRetry(user);
      return ApiResponse.success(null);
    } catch (e) {
      return ApiResponse.error('Failed to save user: ${e.toString()}');
    }
  }

  String getAuthErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'No account found with this email address.';
      case 'wrong-password':
        return 'Incorrect password. Please try again.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'operation-not-allowed':
        return 'This sign-in method is not enabled.';
      case 'invalid-credential':
        return 'Invalid email or password. Please check your credentials and try again.';
      case 'network-request-failed':
        return 'Network error. Please check your connection.';
      default:
        return 'Authentication failed. Please try again.';
    }
  }


  // Purpose: Send password reset email to user's email address
  Future<ApiResponse<bool>> resetPassword({required String email}) async {
    try {
      await _auth.sendPasswordResetEmail(email: email.trim().toLowerCase());
      return ApiResponse.success(true);
    } on FirebaseAuthException catch (e) {
      String errorMessage;
      switch (e.code) {
        case 'user-not-found':
          errorMessage = 'No account found with this email address.';
          break;
        case 'invalid-email':
          errorMessage = 'Please enter a valid email address.';
          break;
        case 'too-many-requests':
          errorMessage = 'Too many requests. Please try again later.';
          break;
        case 'network-request-failed':
          errorMessage = 'Network error. Please check your connection.';
          break;
        default:
          errorMessage = 'Failed to send password reset email. Please try again.';
      }
      return ApiResponse.error(errorMessage);
    } catch (e) {
      return ApiResponse.error('Password reset failed: ${e.toString()}');
    }
  }


  bool isSessionValid() {
    final user = _auth.currentUser;
    if (user == null) return false;

    return true;
  }

  // Purpose: Refresh current user's authentication session
  Future<bool> refreshUserSession() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      await user.getIdToken(true);
      return true;
    } catch (e) {
      print('Session refresh failed: $e');
      return false;
    }
  }

  // Purpose: Sign out user and clear authentication state
  Future<ApiResponse<bool>> signOutUser() async {
    try {
      await _auth.signOut();
      return ApiResponse.success(true);
    } catch (e) {
      return ApiResponse.error('Sign out failed: ${e.toString()}');
    }
  }


  // Purpose: Upload user profile picture to Firebase Storage and return download URL
  Future<ApiResponse<String>> uploadProfilePicture(File imageFile) async {
    try {
      print('FirebaseService: Starting profile picture upload...');

      final user = currentUser;
      if (user == null) {
        print('FirebaseService: No user signed in');
        return ApiResponse.error('No user signed in');
      }

      print('FirebaseService: User authenticated: ${user.uid}');

      if (!await imageFile.exists()) {
        print('FirebaseService: Image file does not exist: ${imageFile.path}');
        return ApiResponse.error('Image file does not exist');
      }

      final fileSize = await imageFile.length();
      print('FirebaseService: Image file size: $fileSize bytes');

      if (fileSize > 10 * 1024 * 1024) {
        return ApiResponse.error('Image file is too large. Please select an image smaller than 10MB.');
      }

      final fileName = 'profile_pictures/${user.uid}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      print('FirebaseService: Upload filename: $fileName');

      final storageRef = _storage.ref().child(fileName);
      print('FirebaseService: Storage reference created');

      print('FirebaseService: Starting file upload...');
      final uploadTask = storageRef.putFile(imageFile);

      final snapshot = await uploadTask.timeout(
        const Duration(minutes: 2),
        onTimeout: () {
          print('FirebaseService: Upload timed out after 2 minutes');
          throw Exception('Upload timed out. Please check your internet connection and try again.');
        },
      );
      print('FirebaseService: Upload completed, getting download URL...');

      final downloadUrl = await snapshot.ref.getDownloadURL();
      print('FirebaseService: Download URL obtained: $downloadUrl');

      return ApiResponse.success(downloadUrl);

    } catch (e) {
      print('FirebaseService: Error uploading profile picture: $e');
      print('FirebaseService: Error type: ${e.runtimeType}');

      if (e is FirebaseException) {
        print('FirebaseService: Firebase error code: ${e.code}');
        print('FirebaseService: Firebase error message: ${e.message}');

        switch (e.code) {
          case 'permission-denied':
            return ApiResponse.error('Permission denied. Please check your account permissions.');
          case 'unauthorized':
            return ApiResponse.error('Unauthorized access. Please sign in again.');
          case 'retry-limit-exceeded':
            return ApiResponse.error('Upload failed due to network issues. Please try again.');
          case 'invalid-checksum':
            return ApiResponse.error('File corrupted during upload. Please try again.');
          default:
            return ApiResponse.error('Upload failed: ${e.message ?? e.code}');
        }
      }

      if (e.toString().contains('timeout') || e.toString().contains('TimeoutException')) {
        return ApiResponse.error('Upload timed out. Please check your internet connection and try again.');
      }

      return ApiResponse.error('Failed to upload profile picture: ${e.toString()}');
    }
  }

  // Purpose: Update user's profile picture URL in Firestore
  Future<ApiResponse<void>> updateUserProfilePicture(String profilePictureUrl) async {
    try {
      final user = currentUser;
      if (user == null) {
        return ApiResponse.error('No user signed in');
      }

      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.uid)
          .update({'profilePictureUrl': profilePictureUrl});

      return ApiResponse.success(null);

    } catch (e) {
      print('Error updating profile picture URL: $e');
      return ApiResponse.error('Failed to update profile picture: ${e.toString()}');
    }
  }

  // Purpose: Delete old profile picture from Firebase Storage
  Future<void> deleteOldProfilePicture(String? oldProfilePictureUrl) async {
    if (oldProfilePictureUrl == null || oldProfilePictureUrl.isEmpty) {
      return;
    }

    try {
      final uri = Uri.parse(oldProfilePictureUrl);
      final path = uri.pathSegments.last;

      final storageRef = _storage.ref().child('profile_pictures/$path');

      await storageRef.delete();

    } catch (e) {
      print('Warning: Failed to delete old profile picture: $e');
    }
  }

  // Purpose: Test Firebase Storage connectivity and permissions
  Future<ApiResponse<String>> testStorageConnectivity() async {
    try {
      print('FirebaseService: Testing Firebase Storage connectivity...');

      final user = currentUser;
      if (user == null) {
        return ApiResponse.error('No user signed in');
      }

      final testRef = _storage.ref().child('test/connectivity_test.txt');
      print('FirebaseService: Storage reference created for test');

      print('FirebaseService: Storage connectivity test passed');
      return ApiResponse.success('Storage is accessible');

    } catch (e) {
      print('FirebaseService: Storage connectivity test error: $e');
      return ApiResponse.error('Storage connectivity test failed: ${e.toString()}');
    }
  }


}