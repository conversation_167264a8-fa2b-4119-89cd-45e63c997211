import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

enum BreathingPhase { inhale, hold, exhale, holdAfterExhale }

enum BreathingPattern {
  fourSevenEight,
  boxBreathing,
  deepCalming,
}

class BreathingPatternConfig {
  final String name;
  final String description;
  final int inhaleSeconds;
  final int holdAfterInhaleSeconds;
  final int exhaleSeconds;
  final int holdAfterExhaleSeconds;
  final int totalCycles;
  final List<String> guidanceMessages;

  const BreathingPatternConfig({
    required this.name,
    required this.description,
    required this.inhaleSeconds,
    required this.holdAfterInhaleSeconds,
    required this.exhaleSeconds,
    required this.holdAfterExhaleSeconds,
    required this.totalCycles,
    required this.guidanceMessages,
  });

  static const Map<BreathingPattern, BreathingPatternConfig> patterns = {
    BreathingPattern.fourSevenEight: BreathingPatternConfig(
      name: '4-7-8 Technique',
      description: 'Perfect for reducing flight anxiety and promoting calm',
      inhaleSeconds: 4,
      holdAfterInhaleSeconds: 7,
      exhaleSeconds: 8,
      holdAfterExhaleSeconds: 0,
      totalCycles: 4,
      guidanceMessages: [
        'Let\'s begin your calming breathing exercise.',
        'Breathe in slowly through your nose.',
        'Hold your breath gently.',
        'Breathe out slowly through your mouth.',
        'You\'re doing wonderfully.',
        'Feel the tension leaving your body.',
        'Almost done, you\'re so relaxed.',
        'Perfect! You\'ve completed your exercise.',
      ],
    ),
    BreathingPattern.boxBreathing: BreathingPatternConfig(
      name: 'Box Breathing',
      description: 'Used by pilots and professionals to stay calm under pressure',
      inhaleSeconds: 4,
      holdAfterInhaleSeconds: 4,
      exhaleSeconds: 4,
      holdAfterExhaleSeconds: 4,
      totalCycles: 5,
      guidanceMessages: [
        'Welcome to box breathing.',
        'Breathe in slowly and steadily.',
        'Hold this breath with confidence.',
        'Exhale slowly, releasing all tension.',
        'Breathe in slowly and steadily.',
        'You\'re building strength and calm.',
        'Feel your confidence growing.',
        'Excellent! You\'re ready for anything.',
      ],
    ),
    BreathingPattern.deepCalming: BreathingPatternConfig(
      name: 'Deep Calming',
      description: 'Gentle, soothing breaths for overall relaxation',
      inhaleSeconds: 6,
      holdAfterInhaleSeconds: 2,
      exhaleSeconds: 6,
      holdAfterExhaleSeconds: 4,
      totalCycles: 6,
      guidanceMessages: [
        'Time for deep, calming breaths.',
        'Breathe in peace and tranquility.',
        'Hold onto this feeling of calm.',
        'Breathe out any worries or fears.',
        'Breathe in peace and tranquility.',
        'You are safe and in control.',
        'Feel the calm surrounding you.',
        'Beautiful! You are completely relaxed.',
      ],
    ),
  };
}

@immutable
abstract class BreathingState extends Equatable {
  const BreathingState();

  @override
  List<Object> get props => [];
}

class BreathingInitial extends BreathingState {}

class BreathingPreparing extends BreathingState {
  final int countdownSeconds;
  final BreathingPattern pattern;
  final String message;

  const BreathingPreparing({
    required this.countdownSeconds,
    required this.pattern,
    required this.message,
  });

  @override
  List<Object> get props => [countdownSeconds, pattern, message];
}

class BreathingInProgress extends BreathingState {
  final int cycle;
  final int totalCycles;
  final BreathingPhase phase;
  final int remainingSeconds;
  final bool isPaused;
  final BreathingPattern pattern;
  final String? currentGuidanceMessage;

  const BreathingInProgress({
    required this.cycle,
    required this.totalCycles,
    required this.phase,
    required this.remainingSeconds,
    this.isPaused = false,
    this.pattern = BreathingPattern.fourSevenEight,
    this.currentGuidanceMessage,
  });

  BreathingInProgress copyWith({
    int? cycle,
    int? totalCycles,
    BreathingPhase? phase,
    int? remainingSeconds,
    bool? isPaused,
    BreathingPattern? pattern,
    String? currentGuidanceMessage,
  }) {
    return BreathingInProgress(
      cycle: cycle ?? this.cycle,
      totalCycles: totalCycles ?? this.totalCycles,
      phase: phase ?? this.phase,
      remainingSeconds: remainingSeconds ?? this.remainingSeconds,
      isPaused: isPaused ?? this.isPaused,
      pattern: pattern ?? this.pattern,
      currentGuidanceMessage: currentGuidanceMessage ?? this.currentGuidanceMessage,
    );
  }

  @override
  List<Object> get props => [
        cycle,
        totalCycles,
        phase,
        remainingSeconds,
        isPaused,
        pattern,
        currentGuidanceMessage ?? '',
      ];
}

class BreathingComplete extends BreathingState {}