import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;
import '../../interfaces/payment_provider_interface.dart';
import '../../models/payment_models.dart';
import '../../models/subscription_model.dart';

// Purpose: PayPal payment provider implementation for subscription management
class PayPalProvider implements PaymentProviderInterface {
  static const String _sandboxBaseUrl = 'https://api.sandbox.paypal.com';
  static const String _liveBaseUrl = 'https://api.paypal.com';
  
  late PaymentConfig _config;
  late String _baseUrl;
  String? _accessToken;
  DateTime? _tokenExpiry;
  
  late Map<SubscriptionPlan, String> _planIds;

  @override
  Future<void> initialize(PaymentConfig config) async {
    _config = config;
    _baseUrl = config.isTestMode ? _sandboxBaseUrl : _liveBaseUrl;
    
    _planIds = Map<SubscriptionPlan, String>.from(config.planIds);
    
    await _getAccessToken();
    
    print('PayPalProvider: Initialized with ${config.isTestMode ? 'sandbox' : 'live'} mode');
  }

  Future<void> _getAccessToken() async {
    if (_accessToken != null && _tokenExpiry != null && DateTime.now().isBefore(_tokenExpiry!)) {
      return;
    }

    try {
      final credentials = base64Encode(utf8.encode('${_config.apiKey}:${_config.secretKey}'));
      
      final response = await http.post(
        Uri.parse('$_baseUrl/v1/oauth2/token'),
        headers: {
          'Authorization': 'Basic $credentials',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'grant_type=client_credentials',
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _accessToken = data['access_token'];
        final expiresIn = data['expires_in'] as int;
        _tokenExpiry = DateTime.now().add(Duration(seconds: expiresIn - 60));
        
        print('PayPalProvider: Access token obtained, expires at $_tokenExpiry');
      } else {
        throw Exception('Failed to get PayPal access token: ${response.statusCode}');
      }
    } catch (e) {
      print('PayPalProvider: Error getting access token: $e');
      throw Exception('PayPal authentication failed: $e');
    }
  }

  Future<Map<String, String>> _getHeaders() async {
    await _getAccessToken();
    return {
      'Authorization': 'Bearer $_accessToken',
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  @override
  Future<PaymentResult> createCustomer({
    required String email,
    required String name,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      final customerId = 'paypal_customer_${DateTime.now().millisecondsSinceEpoch}';
      
      return PaymentResult.success(
        paymentId: 'paypal_customer_created',
        customerId: customerId,
        metadata: {
          'email': email,
          'name': name,
          ...metadata,
        },
      );
    } catch (e) {
      return PaymentResult.failure(error: 'PayPal customer creation failed: $e');
    }
  }

  @override
  Future<PaymentResult> createSubscription({
    required String customerId,
    required SubscriptionPlan plan,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      final planId = _planIds[plan];
      if (planId == null) {
        return PaymentResult.failure(error: 'Plan ID not found for plan: ${plan.name}');
      }

      final headers = await _getHeaders();
      final body = {
        'plan_id': planId,
        'start_time': DateTime.now().add(const Duration(minutes: 1)).toIso8601String(),
        'quantity': '1',
        'shipping_amount': {
          'currency_code': 'USD',
          'value': '0.00',
        },
        'subscriber': {
          'name': {
            'given_name': metadata['name'] ?? 'Customer',
            'surname': '',
          },
          'email_address': metadata['email'] ?? customerId,
        },
        'application_context': {
          'brand_name': 'Flight Fear Wellness',
          'locale': 'en-US',
          'shipping_preference': 'NO_SHIPPING',
          'user_action': 'SUBSCRIBE_NOW',
          'payment_method': {
            'payer_selected': 'PAYPAL',
            'payee_preferred': 'IMMEDIATE_PAYMENT_REQUIRED',
          },
          'return_url': 'https://your-app.com/payment/success',
          'cancel_url': 'https://your-app.com/payment/cancel'
        },
        'custom_id': customerId,
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/v1/billing/subscriptions'),
        headers: headers,
        body: json.encode(body),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return PaymentResult.success(
          paymentId: 'paypal_subscription_${data['id']}',
          subscriptionId: data['id'],
          customerId: customerId,
          metadata: {
            'approval_url': _getApprovalUrl(data['links']),
            'status': data['status'],
          },
        );
      } else {
        final error = json.decode(response.body);
        return PaymentResult.failure(
          error: error['message'] ?? 'Failed to create PayPal subscription',
        );
      }
    } catch (e) {
      return PaymentResult.failure(error: 'PayPal subscription creation failed: $e');
    }
  }

  String? _getApprovalUrl(List<dynamic> links) {
    for (final link in links) {
      if (link['rel'] == 'approve') {
        return link['href'];
      }
    }
    return null;
  }

  @override
  Future<PaymentResult> createPaymentIntent({
    required String customerId,
    required SubscriptionPlan plan,
    required double amount,
    required String currency,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      final headers = await _getHeaders();
      final body = {
        'intent': 'CAPTURE',
        'purchase_units': [
          {
            'amount': {
              'currency_code': currency.toUpperCase(),
              'value': amount.toStringAsFixed(2),
            },
            'description': '${plan.name.toUpperCase()} Plan - Flight Fear Wellness',
            'custom_id': customerId,
          }
        ],
        'application_context': {
          'brand_name': 'Flight Fear Wellness',
          'locale': 'en-US',
          'landing_page': 'BILLING',
          'shipping_preference': 'NO_SHIPPING',
          'user_action': 'PAY_NOW',
          'return_url': 'https://your-app.com/payment/success',
          'cancel_url': 'https://your-app.com/payment/cancel'
        },
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/v2/checkout/orders'),
        headers: headers,
        body: json.encode(body),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return PaymentResult.success(
          paymentId: data['id'],
          customerId: customerId,
          metadata: {
            'approval_url': _getApprovalUrl(data['links']),
            'status': data['status'],
          },
        );
      } else {
        final error = json.decode(response.body);
        return PaymentResult.failure(
          error: error['message'] ?? 'Failed to create PayPal payment',
        );
      }
    } catch (e) {
      return PaymentResult.failure(error: 'PayPal payment creation failed: $e');
    }
  }

  @override
  Future<ProviderSubscription?> getSubscription(String subscriptionId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/v1/billing/subscriptions/$subscriptionId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _createProviderSubscriptionFromPayPal(data);
      }
      return null;
    } catch (e) {
      print('PayPalProvider: Error getting subscription: $e');
      return null;
    }
  }

  ProviderSubscription _createProviderSubscriptionFromPayPal(Map<String, dynamic> data) {
    final planId = data['plan_id'];
    final plan = _planIds.entries
        .firstWhere((entry) => entry.value == planId, orElse: () => const MapEntry(SubscriptionPlan.basic, ''))
        .key;

    return ProviderSubscription(
      id: data['id'],
      customerId: data['custom_id'] ?? data['subscriber']['email_address'],
      plan: plan,
      status: _mapPayPalStatus(data['status']),
      currentPeriodStart: DateTime.parse(data['start_time']),
      currentPeriodEnd: DateTime.parse(data['billing_info']['next_billing_time']),
      cancelAtPeriodEnd: data['status'] == 'CANCELLED',
      amount: double.parse(data['billing_info']['last_payment']['amount']['value']),
      currency: data['billing_info']['last_payment']['amount']['currency_code'],
      provider: PaymentProvider.paypal,
      metadata: {
        'plan_id': planId,
        'subscriber_id': data['subscriber']['payer_id'],
      },
    );
  }

  ProviderSubscriptionStatus _mapPayPalStatus(String status) {
    switch (status) {
      case 'ACTIVE':
        return ProviderSubscriptionStatus.active;
      case 'CANCELLED':
        return ProviderSubscriptionStatus.cancelled;
      case 'SUSPENDED':
        return ProviderSubscriptionStatus.pastDue;
      case 'EXPIRED':
        return ProviderSubscriptionStatus.expired;
      default:
        return ProviderSubscriptionStatus.unpaid;
    }
  }

  @override
  Future<PaymentResult> cancelSubscription({
    required String subscriptionId,
    bool cancelAtPeriodEnd = true,
  }) async {
    try {
      final headers = await _getHeaders();
      final body = {
        'reason': 'User requested cancellation',
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/v1/billing/subscriptions/$subscriptionId/cancel'),
        headers: headers,
        body: json.encode(body),
      );

      if (response.statusCode == 204) {
        return PaymentResult.success(
          paymentId: 'paypal_cancel_$subscriptionId',
          subscriptionId: subscriptionId,
        );
      } else {
        final error = json.decode(response.body);
        return PaymentResult.failure(
          error: error['message'] ?? 'Failed to cancel PayPal subscription',
        );
      }
    } catch (e) {
      return PaymentResult.failure(error: 'PayPal cancellation failed: $e');
    }
  }

  @override
  Future<PaymentResult> updateSubscription({
    required String subscriptionId,
    required SubscriptionPlan newPlan,
  }) async {
    try {
      final planId = _planIds[newPlan];
      if (planId == null) {
        return PaymentResult.failure(error: 'Plan ID not found for plan: ${newPlan.name}');
      }

      final headers = await _getHeaders();
      final body = [
        {
          'op': 'replace',
          'path': '/plan_id',
          'value': planId,
        }
      ];

      final response = await http.patch(
        Uri.parse('$_baseUrl/v1/billing/subscriptions/$subscriptionId'),
        headers: headers,
        body: json.encode(body),
      );

      if (response.statusCode == 204) {
        return PaymentResult.success(
          paymentId: 'paypal_update_$subscriptionId',
          subscriptionId: subscriptionId,
        );
      } else {
        final error = json.decode(response.body);
        return PaymentResult.failure(
          error: error['message'] ?? 'Failed to update PayPal subscription',
        );
      }
    } catch (e) {
      return PaymentResult.failure(error: 'PayPal update failed: $e');
    }
  }

  @override
  Future<PaymentCustomer?> getCustomer(String customerId) async {
    return PaymentCustomer(
      id: customerId,
      email: customerId.contains('@') ? customerId : '<EMAIL>',
      name: 'PayPal Customer',
      provider: PaymentProvider.paypal,
      providerCustomerId: customerId,
    );
  }

  @override
  Future<List<PaymentInvoice>> getCustomerInvoices(String customerId) async {
    return [];
  }

  @override
  bool verifyWebhookSignature({
    required String payload,
    required String signature,
    required String secret,
  }) {
    try {
      final expectedSignature = Hmac(sha256, utf8.encode(secret))
          .convert(utf8.encode(payload))
          .toString();

      return signature == expectedSignature;
    } catch (e) {
      print('PayPalProvider: Webhook signature verification failed: $e');
      return false;
    }
  }

  @override
  Future<PaymentResult> processWebhookEvent(WebhookEvent event) async {
    try {
      print('PayPalProvider: Processing webhook event: ${event.eventType}');

      switch (event.eventType) {
        case 'BILLING.SUBSCRIPTION.CREATED':
          return _handleSubscriptionCreated(event.data);
        case 'BILLING.SUBSCRIPTION.ACTIVATED':
          return _handleSubscriptionActivated(event.data);
        case 'BILLING.SUBSCRIPTION.CANCELLED':
          return _handleSubscriptionCancelled(event.data);
        case 'PAYMENT.SALE.COMPLETED':
          return _handlePaymentCompleted(event.data);
        default:
          print('PayPalProvider: Unhandled webhook event: ${event.eventType}');
          return PaymentResult.success(paymentId: 'paypal_webhook_${event.id}');
      }
    } catch (e) {
      print('PayPalProvider: Error processing webhook: $e');
      return PaymentResult.failure(error: 'PayPal webhook processing failed: $e');
    }
  }

  Future<PaymentResult> _handleSubscriptionCreated(Map<String, dynamic> data) async {
    final subscriptionId = data['id'];
    final customerId = data['custom_id'];

    print('PayPalProvider: Subscription created - ID: $subscriptionId');

    return PaymentResult.success(
      paymentId: 'paypal_sub_created_$subscriptionId',
      subscriptionId: subscriptionId,
      customerId: customerId,
    );
  }

  Future<PaymentResult> _handleSubscriptionActivated(Map<String, dynamic> data) async {
    final subscriptionId = data['id'];

    print('PayPalProvider: Subscription activated - ID: $subscriptionId');

    return PaymentResult.success(
      paymentId: 'paypal_sub_activated_$subscriptionId',
      subscriptionId: subscriptionId,
    );
  }

  Future<PaymentResult> _handleSubscriptionCancelled(Map<String, dynamic> data) async {
    final subscriptionId = data['id'];

    print('PayPalProvider: Subscription cancelled - ID: $subscriptionId');

    return PaymentResult.success(
      paymentId: 'paypal_sub_cancelled_$subscriptionId',
      subscriptionId: subscriptionId,
    );
  }

  Future<PaymentResult> _handlePaymentCompleted(Map<String, dynamic> data) async {
    final paymentId = data['id'];
    final amount = data['amount']['total'];

    print('PayPalProvider: Payment completed - ID: $paymentId, Amount: $amount');

    return PaymentResult.success(
      paymentId: paymentId,
    );
  }

  @override
  String getCheckoutUrl({
    required SubscriptionPlan plan,
    required String customerEmail,
    String? customerId,
    Map<String, dynamic> metadata = const {},
  }) {
    final planId = _planIds[plan];
    if (planId == null) {
      throw ArgumentError('Plan ID not found for plan: ${plan.name}');
    }

    final baseUrl = _config.isTestMode
        ? 'https://www.sandbox.paypal.com/checkoutnow'
        : 'https://www.paypal.com/checkoutnow';

    return '$baseUrl?token=PLACEHOLDER_TOKEN';
  }

  @override
  String? getCustomerPortalUrl(String customerId) {
    return _config.isTestMode
        ? 'https://www.sandbox.paypal.com/myaccount'
        : 'https://www.paypal.com/myaccount';
  }

  @override
  bool validateConfig(PaymentConfig config) {
    return config.apiKey.isNotEmpty &&
           config.secretKey?.isNotEmpty == true &&
           config.planIds.containsKey(SubscriptionPlan.basic) &&
           config.planIds.containsKey(SubscriptionPlan.premium) &&
           config.planIds[SubscriptionPlan.basic]?.isNotEmpty == true &&
           config.planIds[SubscriptionPlan.premium]?.isNotEmpty == true;
  }

  @override
  String get providerName => 'PayPal';

  @override
  List<String> get supportedFeatures => [
        'subscriptions',
        'one-time-payments',
        'webhooks',
        'customer-portal',
      ];
}
