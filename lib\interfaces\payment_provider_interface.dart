import '../models/payment_models.dart';
import '../models/subscription_model.dart';

abstract class PaymentProviderInterface {
  Future<void> initialize(PaymentConfig config);

  Future<PaymentResult> createCustomer({
    required String email,
    required String name,
    Map<String, dynamic> metadata = const {},
  });

  Future<PaymentResult> createSubscription({
    required String customerId,
    required SubscriptionPlan plan,
    Map<String, dynamic> metadata = const {},
  });

  Future<PaymentResult> createPaymentIntent({
    required String customerId,
    required SubscriptionPlan plan,
    required double amount,
    required String currency,
    Map<String, dynamic> metadata = const {},
  });

  Future<ProviderSubscription?> getSubscription(String subscriptionId);

  Future<PaymentResult> cancelSubscription({
    required String subscriptionId,
    bool cancelAtPeriodEnd = true,
  });

  Future<PaymentResult> updateSubscription({
    required String subscriptionId,
    required SubscriptionPlan newPlan,
  });

  Future<PaymentCustomer?> getCustomer(String customerId);

  Future<List<PaymentInvoice>> getCustomerInvoices(String customerId);

  bool verifyWebhookSignature({
    required String payload,
    required String signature,
    required String secret,
  });

  Future<PaymentResult> processWebhookEvent(WebhookEvent event);

  String getCheckoutUrl({
    required SubscriptionPlan plan,
    required String customerEmail,
    String? customerId,
    Map<String, dynamic> metadata = const {},
  });

  String? getCustomerPortalUrl(String customerId);

  bool validateConfig(PaymentConfig config);

  String get providerName;

  List<String> get supportedFeatures;
}

class LemonSqueezyProviderPlaceholder implements PaymentProviderInterface {
  @override
  Future<void> initialize(PaymentConfig config) async {
    throw UnimplementedError('LemonSqueezy provider not yet implemented');
  }

  @override
  Future<PaymentResult> createCustomer({
    required String email,
    required String name,
    Map<String, dynamic> metadata = const {},
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> createSubscription({
    required String customerId,
    required SubscriptionPlan plan,
    Map<String, dynamic> metadata = const {},
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> createPaymentIntent({
    required String customerId,
    required SubscriptionPlan plan,
    required double amount,
    required String currency,
    Map<String, dynamic> metadata = const {},
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<ProviderSubscription?> getSubscription(String subscriptionId) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> cancelSubscription({
    required String subscriptionId,
    bool cancelAtPeriodEnd = true,
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> updateSubscription({
    required String subscriptionId,
    required SubscriptionPlan newPlan,
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentCustomer?> getCustomer(String customerId) async {
    throw UnimplementedError();
  }

  @override
  Future<List<PaymentInvoice>> getCustomerInvoices(String customerId) async {
    throw UnimplementedError();
  }

  @override
  bool verifyWebhookSignature({
    required String payload,
    required String signature,
    required String secret,
  }) {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> processWebhookEvent(WebhookEvent event) async {
    throw UnimplementedError();
  }

  @override
  String getCheckoutUrl({
    required SubscriptionPlan plan,
    required String customerEmail,
    String? customerId,
    Map<String, dynamic> metadata = const {},
  }) {
    throw UnimplementedError();
  }

  @override
  String? getCustomerPortalUrl(String customerId) {
    throw UnimplementedError();
  }

  @override
  bool validateConfig(PaymentConfig config) {
    throw UnimplementedError();
  }

  @override
  String get providerName => 'LemonSqueezy';

  @override
  List<String> get supportedFeatures => [
        'subscriptions',
        'one-time-payments',
        'webhooks',
        'customer-portal',
      ];
}

class StripeProvider implements PaymentProviderInterface {
  @override
  Future<void> initialize(PaymentConfig config) async {
    throw UnimplementedError('Stripe provider not yet implemented');
  }

  @override
  Future<PaymentResult> createCustomer({
    required String email,
    required String name,
    Map<String, dynamic> metadata = const {},
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> createSubscription({
    required String customerId,
    required SubscriptionPlan plan,
    Map<String, dynamic> metadata = const {},
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> createPaymentIntent({
    required String customerId,
    required SubscriptionPlan plan,
    required double amount,
    required String currency,
    Map<String, dynamic> metadata = const {},
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<ProviderSubscription?> getSubscription(String subscriptionId) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> cancelSubscription({
    required String subscriptionId,
    bool cancelAtPeriodEnd = true,
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> updateSubscription({
    required String subscriptionId,
    required SubscriptionPlan newPlan,
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentCustomer?> getCustomer(String customerId) async {
    throw UnimplementedError();
  }

  @override
  Future<List<PaymentInvoice>> getCustomerInvoices(String customerId) async {
    throw UnimplementedError();
  }

  @override
  bool verifyWebhookSignature({
    required String payload,
    required String signature,
    required String secret,
  }) {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> processWebhookEvent(WebhookEvent event) async {
    throw UnimplementedError();
  }

  @override
  String getCheckoutUrl({
    required SubscriptionPlan plan,
    required String customerEmail,
    String? customerId,
    Map<String, dynamic> metadata = const {},
  }) {
    throw UnimplementedError();
  }

  @override
  String? getCustomerPortalUrl(String customerId) {
    throw UnimplementedError();
  }

  @override
  bool validateConfig(PaymentConfig config) {
    throw UnimplementedError();
  }

  @override
  String get providerName => 'Stripe';

  @override
  List<String> get supportedFeatures => [
        'subscriptions',
        'one-time-payments',
        'webhooks',
        'customer-portal',
        'payment-methods',
      ];
}

class PayPalProvider implements PaymentProviderInterface {
  @override
  Future<void> initialize(PaymentConfig config) async {
    throw UnimplementedError('PayPal provider not yet implemented');
  }

  @override
  Future<PaymentResult> createCustomer({
    required String email,
    required String name,
    Map<String, dynamic> metadata = const {},
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> createSubscription({
    required String customerId,
    required SubscriptionPlan plan,
    Map<String, dynamic> metadata = const {},
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> createPaymentIntent({
    required String customerId,
    required SubscriptionPlan plan,
    required double amount,
    required String currency,
    Map<String, dynamic> metadata = const {},
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<ProviderSubscription?> getSubscription(String subscriptionId) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> cancelSubscription({
    required String subscriptionId,
    bool cancelAtPeriodEnd = true,
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> updateSubscription({
    required String subscriptionId,
    required SubscriptionPlan newPlan,
  }) async {
    throw UnimplementedError();
  }

  @override
  Future<PaymentCustomer?> getCustomer(String customerId) async {
    throw UnimplementedError();
  }

  @override
  Future<List<PaymentInvoice>> getCustomerInvoices(String customerId) async {
    throw UnimplementedError();
  }

  @override
  bool verifyWebhookSignature({
    required String payload,
    required String signature,
    required String secret,
  }) {
    throw UnimplementedError();
  }

  @override
  Future<PaymentResult> processWebhookEvent(WebhookEvent event) async {
    throw UnimplementedError();
  }

  @override
  String getCheckoutUrl({
    required SubscriptionPlan plan,
    required String customerEmail,
    String? customerId,
    Map<String, dynamic> metadata = const {},
  }) {
    throw UnimplementedError();
  }

  @override
  String? getCustomerPortalUrl(String customerId) {
    throw UnimplementedError();
  }

  @override
  bool validateConfig(PaymentConfig config) {
    throw UnimplementedError();
  }

  @override
  String get providerName => 'PayPal';

  @override
  List<String> get supportedFeatures => [
        'subscriptions',
        'one-time-payments',
        'webhooks',
      ];
}
