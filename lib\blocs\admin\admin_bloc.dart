import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flight_fear_wellness_app/blocs/admin/admin_event.dart';
import 'package:flight_fear_wellness_app/blocs/admin/admin_state.dart';
import 'package:flight_fear_wellness_app/utils/constants.dart';
import 'package:flight_fear_wellness_app/utils/admin_guard.dart';
import 'package:flight_fear_wellness_app/services/active_session_service.dart';

class AdminBloc extends Bloc<AdminEvent, AdminState> {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  StreamSubscription<QuerySnapshot>? _usersSubscription;
  StreamSubscription<QuerySnapshot>? _dashboardSubscription;
  StreamSubscription<QuerySnapshot>? _activeSessionsSubscription;

  AdminBloc({FirebaseFirestore? firestore, FirebaseAuth? auth})
      : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance,
        super(AdminState.initial()) {
    on<LoadDashboardData>(_onLoadDashboardData);
    on<RefreshDashboardData>(_onRefreshDashboardData);
    on<StartRealtimeUpdates>(_onStartRealtimeUpdates);
    on<StopRealtimeUpdates>(_onStopRealtimeUpdates);
    on<ResetAdminState>(_onResetAdminState);
    on<DashboardDataUpdated>(_onDashboardDataUpdated);
    on<UsersDataUpdated>(_onUsersDataUpdated);
    on<LoadAllUsers>(_onLoadAllUsers);
    on<UpdateUserPlan>(_onUpdateUserPlan);
    on<ResetUserCredits>(_onResetUserCredits);
    on<DeleteUser>(_onDeleteUser);
    on<LoadPlanConfiguration>(_onLoadPlanConfiguration);
    on<UpdatePlanConfiguration>(_onUpdatePlanConfiguration);
    on<SearchUsers>(_onSearchUsers);
    on<FilterUsersByPlan>(_onFilterUsersByPlan);
  }

  @override
  Future<void> close() {
    _usersSubscription?.cancel();
    _dashboardSubscription?.cancel();
    _activeSessionsSubscription?.cancel();
    return super.close();
  }

  Future<void> _onLoadDashboardData(
    LoadDashboardData event,
    Emitter<AdminState> emit,
  ) async {
    try {
      if (!AdminGuard.isCurrentUserAdmin()) {
        print('AdminBloc: Non-admin user attempted to load dashboard data');
        emit(state.copyWith(status: AdminStatus.error, error: 'Admin access required'));
        return;
      }

      emit(state.copyWith(status: AdminStatus.loading));

      final stats = await _getDashboardStats();
      emit(state.copyWith(
        status: AdminStatus.loaded,
        dashboardStats: stats,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: AdminStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onRefreshDashboardData(
    RefreshDashboardData event,
    Emitter<AdminState> emit,
  ) async {
    add(LoadDashboardData());
  }

  Future<void> _onStartRealtimeUpdates(
    StartRealtimeUpdates event,
    Emitter<AdminState> emit,
  ) async {
    try {
      if (!AdminGuard.isCurrentUserAdmin()) {
        print('AdminBloc: Non-admin user attempted to start realtime updates');
        return;
      }

      _usersSubscription = _firestore
          .collection(AppConstants.usersCollection)
          .snapshots()
          .listen((snapshot) async {
        print('AdminBloc: Real-time update - received ${snapshot.docs.length} user documents');

        final firestoreUsers = <AdminUser>[];
        for (final doc in snapshot.docs) {
          try {
            final user = AdminUser.fromFirestore(doc.data(), doc.id);
            firestoreUsers.add(user);
            print('AdminBloc: Real-time - parsed user: ${user.email}');
          } catch (e) {
            print('AdminBloc: Real-time - error parsing user ${doc.id}: $e');
          }
        }

        final deduplicatedUsers = _deduplicateUsers(firestoreUsers);
        final validUsers = await _filterValidUsers(deduplicatedUsers);

        print('AdminBloc: Real-time - final user count: ${validUsers.length}');

        add(UsersDataUpdated(users: validUsers));

        final stats = await _calculateAccurateDashboardStats(validUsers);
        add(DashboardDataUpdated(stats: stats));
      });

      _activeSessionsSubscription = _firestore
          .collection('status')
          .snapshots()
          .listen((snapshot) async {
        await ActiveSessionService.cleanupStaleSessions();

        final validUsers = state.users;
        if (validUsers.isNotEmpty) {
          final stats = await _calculateAccurateDashboardStats(validUsers);
          add(DashboardDataUpdated(stats: stats));
        }
      });
    } catch (e) {
      emit(state.copyWith(
        status: AdminStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onStopRealtimeUpdates(
    StopRealtimeUpdates event,
    Emitter<AdminState> emit,
  ) async {
    await _usersSubscription?.cancel();
    await _dashboardSubscription?.cancel();
    _usersSubscription = null;
    _dashboardSubscription = null;
  }

  Future<void> _onResetAdminState(
    ResetAdminState event,
    Emitter<AdminState> emit,
  ) async {
    await _usersSubscription?.cancel();
    await _dashboardSubscription?.cancel();
    await _activeSessionsSubscription?.cancel();
    _usersSubscription = null;
    _dashboardSubscription = null;
    _activeSessionsSubscription = null;

    emit(AdminState.initial());
  }

  Future<void> _onDashboardDataUpdated(
    DashboardDataUpdated event,
    Emitter<AdminState> emit,
  ) async {
    emit(state.copyWith(
      status: AdminStatus.loaded,
      dashboardStats: event.stats,
    ));
  }

  Future<void> _onUsersDataUpdated(
    UsersDataUpdated event,
    Emitter<AdminState> emit,
  ) async {
    List<AdminUser> filteredUsers = event.users;

    if (state.searchQuery != null && state.searchQuery!.isNotEmpty) {
      final query = state.searchQuery!.toLowerCase();
      filteredUsers = filteredUsers.where((user) {
        return user.email.toLowerCase().contains(query) ||
               user.name.toLowerCase().contains(query);
      }).toList();
    }

    if (state.planFilter != null && state.planFilter!.isNotEmpty) {
      filteredUsers = filteredUsers.where((user) => user.plan == state.planFilter).toList();
    }

    emit(state.copyWith(
      status: AdminStatus.loaded,
      users: event.users,
      filteredUsers: filteredUsers,
    ));
  }

  Future<void> _onLoadAllUsers(
    LoadAllUsers event,
    Emitter<AdminState> emit,
  ) async {
    try {
      AdminGuard.requireAdminAccess();
      emit(state.copyWith(status: AdminStatus.loading));

      final users = await _getAllUsers();
      emit(state.copyWith(
        status: AdminStatus.loaded,
        users: users,
        filteredUsers: users,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: AdminStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onUpdateUserPlan(
    UpdateUserPlan event,
    Emitter<AdminState> emit,
  ) async {
    try {
      AdminGuard.requireAdminAccess();

      final planConfigDoc = await _firestore
          .collection(AppConstants.appConfigCollection)
          .doc('plan_configuration')
          .get();

      Map<String, Map<String, dynamic>> planConfig;
      if (planConfigDoc.exists) {
        planConfig = Map<String, Map<String, dynamic>>.from(planConfigDoc.data()!);
      } else {
        planConfig = PlanConfiguration.defaultConfig().plans;
      }

      final newPlanData = planConfig[event.newPlan];
      if (newPlanData == null) {
        throw Exception('Invalid plan: ${event.newPlan}');
      }

      final simplePlanCredits = newPlanData['credits'] as Map<String, dynamic>;

      final fullCreditsStructure = <String, dynamic>{};
      for (final entry in simplePlanCredits.entries) {
        final creditType = entry.key;
        final creditLimit = entry.value as int;

        fullCreditsStructure[creditType] = {
          'limit': creditLimit,
          'used': 0,
          'remaining': creditLimit,
        };
      }

      fullCreditsStructure['resetDate'] = FieldValue.serverTimestamp();

      final now = DateTime.now();
      final endDate = event.newPlan == 'free'
          ? now.add(const Duration(days: 7))
          : now.add(const Duration(days: 30));

      final subscriptionData = {
        'plan': event.newPlan,
        'status': 'active',
        'startDate': FieldValue.serverTimestamp(),
        'endDate': Timestamp.fromDate(endDate),
        'autoRenew': event.newPlan != 'free',
        'trialUsed': event.newPlan != 'free',
        'trialEndsAt': event.newPlan == 'free' ? Timestamp.fromDate(endDate) : null,
        'paymentPlatform': null,
        'subscriptionId': null,
        'customerId': null,
      };

      await _firestore.collection(AppConstants.usersCollection).doc(event.userId).update({
        'subscription': subscriptionData,
        'credits': fullCreditsStructure,
        'updatedAt': FieldValue.serverTimestamp(),
        'planUpdatedBy': AdminGuard.getCurrentAdminEmail(),
        'planUpdatedAt': FieldValue.serverTimestamp(),
      });

      await _notifyUserOfPlanChange(event.userId, event.newPlan);

      await _logAdminAction(
        'update_user_plan',
        'Updated user ${event.userId} to plan ${event.newPlan}',
        {'userId': event.userId, 'newPlan': event.newPlan, 'credits': fullCreditsStructure},
      );

    } catch (e) {
      emit(state.copyWith(
        status: AdminStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onResetUserCredits(
    ResetUserCredits event,
    Emitter<AdminState> emit,
  ) async {
    try {
      AdminGuard.requireAdminAccess();

      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(event.userId)
          .get();

      if (!userDoc.exists) {
        throw Exception('User not found');
      }

      final userData = userDoc.data()!;
      final userPlan = _extractUserPlan(userData);
      final planConfig = PlanConfiguration.defaultConfig();
      final planData = planConfig.plans[userPlan];

      if (planData == null) {
        throw Exception('Invalid user plan: $userPlan');
      }

      final simplePlanCredits = planData['credits'] as Map<String, dynamic>;

      final fullCreditsStructure = <String, dynamic>{};
      for (final entry in simplePlanCredits.entries) {
        final creditType = entry.key;
        final creditLimit = entry.value as int;

        fullCreditsStructure[creditType] = {
          'limit': creditLimit,
          'used': 0,
          'remaining': creditLimit,
        };
      }

      fullCreditsStructure['resetDate'] = FieldValue.serverTimestamp();

      await _firestore.collection(AppConstants.usersCollection).doc(event.userId).update({
        'credits': fullCreditsStructure,
        'updatedAt': FieldValue.serverTimestamp(),
        'creditsResetBy': AdminGuard.getCurrentAdminEmail(),
        'creditsResetAt': FieldValue.serverTimestamp(),
      });

      await _notifyUserOfCreditsReset(event.userId, fullCreditsStructure);

      await _logAdminAction(
        'reset_user_credits',
        'Reset credits for user ${event.userId}',
        {'userId': event.userId, 'plan': userPlan, 'newCredits': fullCreditsStructure},
      );

    } catch (e) {
      emit(state.copyWith(
        status: AdminStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onDeleteUser(
    DeleteUser event,
    Emitter<AdminState> emit,
  ) async {
    try {
      AdminGuard.requireAdminAccess();

      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(event.userId)
          .get();

      String userEmail = 'unknown';
      if (userDoc.exists) {
        userEmail = userDoc.data()?['email'] ?? 'unknown';
      }

      await _firestore.collection(AppConstants.usersCollection).doc(event.userId).delete();

      await _deleteUserRelatedData(event.userId);


      await _logAdminAction(
        'delete_user',
        'Deleted user ${event.userId} ($userEmail) from Firestore',
        {
          'userId': event.userId,
          'userEmail': userEmail,
          'deletedBy': AdminGuard.getCurrentAdminEmail(),
          'note': 'Firebase Auth deletion requires server-side implementation'
        },
      );

    } catch (e) {
      emit(state.copyWith(
        status: AdminStatus.error,
        error: 'Failed to delete user: ${e.toString()}',
      ));
    }
  }

  Future<void> _deleteUserRelatedData(String userId) async {
    final batch = _firestore.batch();

    try {
      final chatMessages = await _firestore
          .collection(AppConstants.chatMessagesCollection)
          .where('userId', isEqualTo: userId)
          .get();

      for (final doc in chatMessages.docs) {
        batch.delete(doc.reference);
      }

      final voiceSessions = await _firestore
          .collection(AppConstants.voiceSessionsCollection)
          .where('userId', isEqualTo: userId)
          .get();

      for (final doc in voiceSessions.docs) {
        batch.delete(doc.reference);
      }

      final videoSessions = await _firestore
          .collection(AppConstants.videoSessionsCollection)
          .where('userId', isEqualTo: userId)
          .get();

      for (final doc in videoSessions.docs) {
        batch.delete(doc.reference);
      }

      try {
        batch.delete(_firestore.collection('subscriptions').doc(userId));
      } catch (e) {
      }

      final notifications = await _firestore
          .collection('user_notifications')
          .where('userId', isEqualTo: userId)
          .get();

      for (final doc in notifications.docs) {
        batch.delete(doc.reference);
      }

      try {
        batch.delete(_firestore.collection('real_time_updates').doc(userId));
      } catch (e) {
      }

      try {
        batch.delete(_firestore.collection('status').doc(userId));
      } catch (e) {
      }

      await batch.commit();
    } catch (e) {
      print('Error deleting related user data: $e');
    }
  }

  Future<void> _onLoadPlanConfiguration(
    LoadPlanConfiguration event,
    Emitter<AdminState> emit,
  ) async {
    try {
      AdminGuard.requireAdminAccess();
      
      final planConfig = await _getPlanConfiguration();
      emit(state.copyWith(
        status: AdminStatus.loaded,
        planConfiguration: planConfig,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: AdminStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onUpdatePlanConfiguration(
    UpdatePlanConfiguration event,
    Emitter<AdminState> emit,
  ) async {
    try {
      AdminGuard.requireAdminAccess();

      emit(state.copyWith(status: AdminStatus.loading));

      final currentConfigDoc = await _firestore
          .collection(AppConstants.appConfigCollection)
          .doc('plan_configuration')
          .get();

      Map<String, Map<String, dynamic>> currentConfig = {};
      if (currentConfigDoc.exists) {
        currentConfig = Map<String, Map<String, dynamic>>.from(currentConfigDoc.data()!);
      }

      await _firestore
          .collection(AppConstants.appConfigCollection)
          .doc('plan_configuration')
          .set(event.planConfig, SetOptions(merge: true));

      final newConfigTyped = Map<String, Map<String, dynamic>>.from(
        event.planConfig.map((key, value) => MapEntry(key, Map<String, dynamic>.from(value)))
      );
      await _propagatePlanChangesToUsers(currentConfig, newConfigTyped);

      await _notifyAllUsersOfPlanConfigChange(newConfigTyped);

      await _logAdminAction(
        'update_plan_config',
        'Updated plan configuration and propagated to all users',
        {
          'newConfig': event.planConfig,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      emit(state.copyWith(status: AdminStatus.loaded));
      add(LoadPlanConfiguration());
    } catch (e) {
      emit(state.copyWith(
        status: AdminStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onSearchUsers(
    SearchUsers event,
    Emitter<AdminState> emit,
  ) async {
    final query = event.query.toLowerCase();
    final filteredUsers = state.users.where((user) {
      return user.email.toLowerCase().contains(query) ||
             user.name.toLowerCase().contains(query);
    }).toList();

    emit(state.copyWith(
      filteredUsers: filteredUsers,
      searchQuery: event.query,
    ));
  }

  Future<void> _onFilterUsersByPlan(
    FilterUsersByPlan event,
    Emitter<AdminState> emit,
  ) async {
    List<AdminUser> filteredUsers;
    
    if (event.planFilter == null || event.planFilter!.isEmpty) {
      filteredUsers = state.users;
    } else {
      filteredUsers = state.users.where((user) => user.plan == event.planFilter).toList();
    }

    emit(state.copyWith(
      filteredUsers: filteredUsers,
      planFilter: event.planFilter,
    ));
  }

  Future<DashboardStats> _getDashboardStats() async {
    try {
      final usersSnapshot = await _firestore.collection(AppConstants.usersCollection).get();
      print('AdminBloc: Fetched ${usersSnapshot.docs.length} user documents from Firestore');

      final users = <AdminUser>[];
      for (final doc in usersSnapshot.docs) {
        try {
          final user = AdminUser.fromFirestore(doc.data(), doc.id);
          users.add(user);
          print('AdminBloc: Successfully parsed user: ${user.email}');
        } catch (e) {
          print('AdminBloc: Error parsing user document ${doc.id}: $e');
          print('AdminBloc: Document data: ${doc.data()}');
        }
      }

      print('AdminBloc: Successfully parsed ${users.length} users');

      final deduplicatedUsers = _deduplicateUsers(users);
      print('AdminBloc: After deduplication: ${deduplicatedUsers.length} users');

      return _calculateStatsFromUsers(deduplicatedUsers);
    } catch (e) {
      print('AdminBloc: Error in _getDashboardStats: $e');
      rethrow;
    }
  }

  DashboardStats _calculateStatsFromUsers(List<AdminUser> users) {
    final totalUsers = users.length;
    final planBreakdown = <String, int>{
      'free': 0,
      'basic': 0,
      'premium': 0,
    };

    final now = DateTime.now();
    final sevenDaysAgo = now.subtract(const Duration(days: 7));
    final activeUsers = users.where((user) => user.lastActive.isAfter(sevenDaysAgo)).length;

    for (final user in users) {
      final plan = user.plan.toLowerCase();
      if (planBreakdown.containsKey(plan)) {
        planBreakdown[plan] = planBreakdown[plan]! + 1;
      } else {
        planBreakdown[plan] = (planBreakdown[plan] ?? 0) + 1;
      }
    }

    return DashboardStats(
      totalUsers: totalUsers,
      activeUsers: activeUsers,
      planBreakdown: planBreakdown,
      dailyUsage: 0,
      totalRevenue: 0.0,
    );
  }

  Future<List<AdminUser>> _getAllUsers() async {
    try {
      final snapshot = await _firestore.collection(AppConstants.usersCollection).get();
      print('AdminBloc: _getAllUsers - fetched ${snapshot.docs.length} documents');

      final users = <AdminUser>[];
      for (final doc in snapshot.docs) {
        try {
          final user = AdminUser.fromFirestore(doc.data(), doc.id);
          users.add(user);
          print('AdminBloc: _getAllUsers - parsed user: ${user.email}');
        } catch (e) {
          print('AdminBloc: _getAllUsers - error parsing user ${doc.id}: $e');
        }
      }

      print('AdminBloc: _getAllUsers - total parsed users: ${users.length}');

      final deduplicatedUsers = _deduplicateUsers(users);
      print('AdminBloc: _getAllUsers - after deduplication: ${deduplicatedUsers.length}');

      return deduplicatedUsers;
    } catch (e) {
      print('AdminBloc: _getAllUsers - error: $e');
      rethrow;
    }
  }

  List<AdminUser> _deduplicateUsers(List<AdminUser> users) {
    final Map<String, AdminUser> uniqueUsers = {};

    for (final user in users) {
      final email = user.email.toLowerCase().trim();

      if (!uniqueUsers.containsKey(email) ||
          user.lastActive.isAfter(uniqueUsers[email]!.lastActive)) {
        uniqueUsers[email] = user;
      }
    }

    final deduplicatedUsers = uniqueUsers.values.toList();
    deduplicatedUsers.sort((a, b) {
      final aIsAdmin = AdminGuard.isAdminEmail(a.email);
      final bIsAdmin = AdminGuard.isAdminEmail(b.email);

      if (aIsAdmin && !bIsAdmin) return -1;
      if (!aIsAdmin && bIsAdmin) return 1;

      return a.name.toLowerCase().compareTo(b.name.toLowerCase());
    });

    return deduplicatedUsers;
  }

  Future<PlanConfiguration> _getPlanConfiguration() async {
    try {
      final doc = await _firestore
          .collection(AppConstants.appConfigCollection)
          .doc('plans')
          .get();
      
      if (doc.exists && doc.data() != null) {
        return PlanConfiguration(plans: Map<String, Map<String, dynamic>>.from(doc.data()!));
      }
    } catch (e) {
      print('Error loading plan configuration: $e');
    }
    
    return PlanConfiguration.defaultConfig();
  }

  Future<void> _logAdminAction(String action, String description, Map<String, dynamic> details) async {
    final adminEmail = AdminGuard.getCurrentAdminEmail();

    await _firestore.collection(AppConstants.adminLogsCollection).add({
      'action': action,
      'description': description,
      'details': details,
      'adminEmail': adminEmail,
      'timestamp': FieldValue.serverTimestamp(),
    });
  }

  Future<List<AdminUser>> _filterValidUsers(List<AdminUser> firestoreUsers) async {
    return firestoreUsers;
  }

  Future<DashboardStats> _calculateAccurateDashboardStats(List<AdminUser> firestoreUsers) async {
    final deduplicatedUsers = _deduplicateUsers(firestoreUsers);
    final totalUsers = deduplicatedUsers.length;

    final activeUsers = await _getActiveUserCount();

    final planBreakdown = <String, int>{
      'free': 0,
      'basic': 0,
      'premium': 0,
    };

    for (final user in deduplicatedUsers) {
      final plan = user.plan.toLowerCase();
      if (planBreakdown.containsKey(plan)) {
        planBreakdown[plan] = planBreakdown[plan]! + 1;
      } else {
        planBreakdown[plan] = (planBreakdown[plan] ?? 0) + 1;
      }
    }

    return DashboardStats(
      totalUsers: totalUsers,
      activeUsers: activeUsers,
      planBreakdown: planBreakdown,
      dailyUsage: 0,
      totalRevenue: 0.0,
    );
  }

  Future<int> _getActiveUserCount() async {
    await ActiveSessionService.cleanupStaleSessions();
    return await ActiveSessionService.getActiveUserCount();
  }

  String _extractUserPlan(Map<String, dynamic> userData) {
    if (userData.containsKey('plan') && userData['plan'] is String) {
      return userData['plan'];
    }

    if (userData.containsKey('subscription') && userData['subscription'] is Map<String, dynamic>) {
      final subscription = userData['subscription'] as Map<String, dynamic>;
      if (subscription.containsKey('plan') && subscription['plan'] is String) {
        return subscription['plan'];
      }
    }

    return 'free';
  }



  Future<void> _notifyUserOfPlanChange(String userId, String newPlan) async {
    try {
      await _firestore.collection('user_notifications').add({
        'userId': userId,
        'type': 'plan_change',
        'title': 'Plan Updated',
        'message': 'Your plan has been updated to ${newPlan.toUpperCase()}',
        'data': {
          'newPlan': newPlan,
          'updatedBy': 'admin',
        },
        'timestamp': FieldValue.serverTimestamp(),
        'read': false,
      });

      await _firestore.collection('real_time_updates').doc(userId).set({
        'type': 'plan_update',
        'newPlan': newPlan,
        'timestamp': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
    } catch (e) {
      print('Error notifying user of plan change: $e');
    }
  }

  Future<void> _notifyUserOfCreditsReset(String userId, Map<String, dynamic> newCredits) async {
    try {
      await _firestore.collection('user_notifications').add({
        'userId': userId,
        'type': 'credits_reset',
        'title': 'Credits Reset',
        'message': 'Your credits have been reset by an administrator',
        'data': {
          'newCredits': newCredits,
          'updatedBy': 'admin',
        },
        'timestamp': FieldValue.serverTimestamp(),
        'read': false,
      });

      await _firestore.collection('real_time_updates').doc(userId).set({
        'type': 'credits_reset',
        'newCredits': newCredits,
        'timestamp': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
    } catch (e) {
      print('Error notifying user of credits reset: $e');
    }
  }

  Future<void> _propagatePlanChangesToUsers(
    Map<String, Map<String, dynamic>> oldConfig,
    Map<String, Map<String, dynamic>> newConfig,
  ) async {
    try {
      final usersSnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .get();

      final batch = _firestore.batch();
      int updatedUsersCount = 0;

      for (final userDoc in usersSnapshot.docs) {
        final userData = userDoc.data();
        final userPlan = _extractUserPlan(userData);

        if (!newConfig.containsKey(userPlan)) continue;

        final newPlanData = newConfig[userPlan]!;
        final oldPlanData = oldConfig[userPlan];

        final newCredits = newPlanData['credits'] as Map<String, dynamic>?;
        final oldCredits = oldPlanData?['credits'] as Map<String, dynamic>?;

        if (newCredits != null && newCredits != oldCredits) {
          final fullCreditsStructure = <String, dynamic>{};
          for (final entry in newCredits.entries) {
            final creditType = entry.key;
            final creditLimit = entry.value as int;

            fullCreditsStructure[creditType] = {
              'limit': creditLimit,
              'used': 0,
              'remaining': creditLimit,
            };
          }

          fullCreditsStructure['resetDate'] = FieldValue.serverTimestamp();

          batch.update(userDoc.reference, {
            'credits': fullCreditsStructure,
            'updatedAt': FieldValue.serverTimestamp(),
            'planConfigUpdatedBy': AdminGuard.getCurrentAdminEmail(),
            'planConfigUpdatedAt': FieldValue.serverTimestamp(),
          });

          updatedUsersCount++;
        }
      }

      if (updatedUsersCount > 0) {
        await batch.commit();
        print('Updated $updatedUsersCount users with new plan configuration');
      }
    } catch (e) {
      print('Error propagating plan changes to users: $e');
    }
  }

  Future<void> _notifyAllUsersOfPlanConfigChange(Map<String, Map<String, dynamic>> newConfig) async {
    try {
      await _firestore.collection('global_notifications').add({
        'type': 'plan_config_update',
        'title': 'Plan Benefits Updated',
        'message': 'Plan benefits have been updated. Check your account for the latest credits and features.',
        'data': {
          'updatedBy': 'admin',
          'timestamp': DateTime.now().toIso8601String(),
        },
        'timestamp': FieldValue.serverTimestamp(),
        'active': true,
      });

      await _firestore.collection('real_time_updates').doc('global_plan_config').set({
        'type': 'plan_config_update',
        'newConfig': newConfig,
        'timestamp': FieldValue.serverTimestamp(),
        'version': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      print('Error notifying users of plan config change: $e');
    }
  }
}
