import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import '../../services/notification_service.dart';
import '../../models/notification_model.dart';
import 'notification_event.dart';
import 'notification_state.dart';

class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final NotificationService _notificationService;
  StreamSubscription<List<NotificationModel>>? _notificationsSubscription;
  StreamSubscription<int>? _unreadCountSubscription;

  NotificationBloc({
    NotificationService? notificationService,
  })  : _notificationService = notificationService ?? NotificationService(),
        super(NotificationState.initial()) {
    on<LoadNotifications>(_onLoadNotifications);
    on<NotificationsUpdated>(_onNotificationsUpdated);
    on<UnreadCountUpdated>(_onUnreadCountUpdated);
    on<MarkNotificationAsRead>(_onMarkNotificationAsRead);
    on<MarkAllNotificationsAsRead>(_onMarkAllNotificationsAsRead);
    on<DeleteNotification>(_onDeleteNotification);
    on<DeleteAllNotifications>(_onDeleteAllNotifications);
    on<RefreshNotifications>(_onRefreshNotifications);
    on<CreateNotification>(_onCreateNotification);
  }

  Future<void> _onLoadNotifications(
    LoadNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      debugPrint('NotificationBloc: Loading notifications...');
      emit(state.copyWith(status: NotificationStatus.loading));

      await _notificationsSubscription?.cancel();
      _notificationsSubscription = _notificationService
          .getUserNotifications(limit: event.limit)
          .listen(
            (notifications) {
              debugPrint('NotificationBloc: Received ${notifications.length} notifications');
              add(NotificationsUpdated(notifications));
            },
            onError: (error) {
              debugPrint('NotificationBloc: Error in notifications stream: $error');
              emit(state.copyWith(
                status: NotificationStatus.error,
                error: error.toString(),
              ));
            },
          );

      await _unreadCountSubscription?.cancel();
      _unreadCountSubscription = _notificationService
          .getUnreadCount()
          .listen(
            (count) => add(UnreadCountUpdated(count)),
            onError: (error) {
              debugPrint('NotificationBloc: Error in unread count stream: $error');
            },
          );

      // Add a timeout to prevent perpetual loading
      Future.delayed(const Duration(seconds: 10), () {
        if (state.status == NotificationStatus.loading) {
          debugPrint('NotificationBloc: Timeout reached, setting to loaded state');
          add(const NotificationsUpdated([]));
        }
      });
    } catch (e) {
      debugPrint('NotificationBloc: Error loading notifications: $e');
      emit(state.copyWith(
        status: NotificationStatus.error,
        error: e.toString(),
      ));
    }
  }

  void _onNotificationsUpdated(
    NotificationsUpdated event,
    Emitter<NotificationState> emit,
  ) {
    emit(state.copyWith(
      status: NotificationStatus.loaded,
      notifications: event.notifications,
      isRefreshing: false,
    ));
  }

  void _onUnreadCountUpdated(
    UnreadCountUpdated event,
    Emitter<NotificationState> emit,
  ) {
    emit(state.copyWith(unreadCount: event.count));
  }

  Future<void> _onMarkNotificationAsRead(
    MarkNotificationAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final success = await _notificationService.markAsRead(event.notificationId);
      if (!success) {
        emit(state.copyWith(
          error: 'Failed to mark notification as read',
        ));
      }
    } catch (e) {
      debugPrint('NotificationBloc: Error marking notification as read: $e');
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> _onMarkAllNotificationsAsRead(
    MarkAllNotificationsAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final success = await _notificationService.markAllAsRead();
      if (!success) {
        emit(state.copyWith(
          error: 'Failed to mark all notifications as read',
        ));
      }
    } catch (e) {
      debugPrint('NotificationBloc: Error marking all notifications as read: $e');
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> _onDeleteNotification(
    DeleteNotification event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final success = await _notificationService.deleteNotification(event.notificationId);
      if (!success) {
        emit(state.copyWith(
          error: 'Failed to delete notification',
        ));
      }
    } catch (e) {
      debugPrint('NotificationBloc: Error deleting notification: $e');
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> _onDeleteAllNotifications(
    DeleteAllNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final success = await _notificationService.deleteAllNotifications();
      if (!success) {
        emit(state.copyWith(
          error: 'Failed to delete all notifications',
        ));
      }
    } catch (e) {
      debugPrint('NotificationBloc: Error deleting all notifications: $e');
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> _onRefreshNotifications(
    RefreshNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    emit(state.copyWith(isRefreshing: true));
  }

  Future<void> _onCreateNotification(
    CreateNotification event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _notificationService.createNotification(event.notification);
    } catch (e) {
      debugPrint('NotificationBloc: Error creating notification: $e');
      emit(state.copyWith(error: e.toString()));
    }
  }

  @override
  Future<void> close() {
    _notificationsSubscription?.cancel();
    _unreadCountSubscription?.cancel();
    return super.close();
  }
}
