import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;
import '../../interfaces/payment_provider_interface.dart';
import '../../models/payment_models.dart';
import '../../models/subscription_model.dart';

// Purpose: Stripe payment provider implementation for subscription management
class StripeProvider implements PaymentProviderInterface {
  static const String _baseUrl = 'https://api.stripe.com/v1';
  
  late PaymentConfig _config;
  late Map<String, String> _headers;
  
  late Map<SubscriptionPlan, String> _priceIds;

  @override
  Future<void> initialize(PaymentConfig config) async {
    _config = config;
    
    _headers = {
      'Authorization': 'Bearer ${config.secretKey}',
      'Content-Type': 'application/x-www-form-urlencoded',
      'Stripe-Version': '2023-10-16',
    };
    
    _priceIds = Map<SubscriptionPlan, String>.from(config.planIds);
    
    print('StripeProvider: Initialized with ${config.isTestMode ? 'test' : 'live'} mode');
  }

  @override
  Future<PaymentResult> createCustomer({
    required String email,
    required String name,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      final body = {
        'email': email,
        'name': name,
        'metadata[app]': 'flight_fear_wellness',
        'metadata[created_by]': 'flutter_app',
      };
      
      metadata.forEach((key, value) {
        body['metadata[$key]'] = value.toString();
      });

      final response = await http.post(
        Uri.parse('$_baseUrl/customers'),
        headers: _headers,
        body: body,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return PaymentResult.success(
          paymentId: 'stripe_customer_${data['id']}',
          customerId: data['id'],
        );
      } else {
        final error = json.decode(response.body);
        return PaymentResult.failure(
          error: error['error']['message'] ?? 'Failed to create customer',
        );
      }
    } catch (e) {
      return PaymentResult.failure(error: 'Network error: $e');
    }
  }

  @override
  Future<PaymentResult> createSubscription({
    required String customerId,
    required SubscriptionPlan plan,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      final priceId = _priceIds[plan];
      if (priceId == null) {
        return PaymentResult.failure(error: 'Price ID not found for plan: ${plan.name}');
      }

      final body = {
        'customer': customerId,
        'items[0][price]': priceId,
        'payment_behavior': 'default_incomplete',
        'payment_settings[save_default_payment_method]': 'on_subscription',
        'expand[]': 'latest_invoice.payment_intent',
        'metadata[plan]': plan.name,
        'metadata[app]': 'flight_fear_wellness',
      };
      
      metadata.forEach((key, value) {
        body['metadata[$key]'] = value.toString();
      });

      final response = await http.post(
        Uri.parse('$_baseUrl/subscriptions'),
        headers: _headers,
        body: body,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return PaymentResult.success(
          paymentId: 'stripe_subscription_${data['id']}',
          subscriptionId: data['id'],
          metadata: {
            'client_secret': data['latest_invoice']['payment_intent']['client_secret'],
          },
        );
      } else {
        final error = json.decode(response.body);
        return PaymentResult.failure(
          error: error['error']['message'] ?? 'Failed to create subscription',
        );
      }
    } catch (e) {
      return PaymentResult.failure(error: 'Network error: $e');
    }
  }

  @override
  Future<PaymentResult> createPaymentIntent({
    required String customerId,
    required SubscriptionPlan plan,
    required double amount,
    required String currency,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      final body = {
        'amount': (amount * 100).round().toString(),
        'currency': currency.toLowerCase(),
        'customer': customerId,
        'automatic_payment_methods[enabled]': 'true',
        'metadata[plan]': plan.name,
        'metadata[app]': 'flight_fear_wellness',
      };
      
      metadata.forEach((key, value) {
        body['metadata[$key]'] = value.toString();
      });

      final response = await http.post(
        Uri.parse('$_baseUrl/payment_intents'),
        headers: _headers,
        body: body,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return PaymentResult.success(
          paymentId: data['id'],
          metadata: {
            'client_secret': data['client_secret'],
            'type': 'payment_intent',
          },
        );
      } else {
        final error = json.decode(response.body);
        return PaymentResult.failure(
          error: error['error']['message'] ?? 'Failed to create payment intent',
        );
      }
    } catch (e) {
      return PaymentResult.failure(error: 'Network error: $e');
    }
  }

  @override
  Future<ProviderSubscription?> getSubscription(String subscriptionId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/subscriptions/$subscriptionId'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _createProviderSubscriptionFromStripe(data);
      }
      return null;
    } catch (e) {
      print('StripeProvider: Error getting subscription: $e');
      return null;
    }
  }

  ProviderSubscription _createProviderSubscriptionFromStripe(Map<String, dynamic> data) {
    final planName = data['metadata']?['plan'] ?? 'basic';
    final plan = SubscriptionPlan.values.firstWhere(
      (p) => p.name == planName,
      orElse: () => SubscriptionPlan.basic,
    );

    return ProviderSubscription(
      id: data['id'],
      customerId: data['customer'],
      plan: plan,
      status: _mapStripeStatus(data['status']),
      currentPeriodStart: DateTime.fromMillisecondsSinceEpoch(data['current_period_start'] * 1000),
      currentPeriodEnd: DateTime.fromMillisecondsSinceEpoch(data['current_period_end'] * 1000),
      cancelAtPeriodEnd: data['cancel_at_period_end'] ?? false,
      amount: (data['items']?['data']?[0]?['price']?['unit_amount'] ?? 0) / 100.0,
      currency: data['items']?['data']?[0]?['price']?['currency'] ?? 'usd',
      provider: PaymentProvider.stripe,
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
    );
  }

  ProviderSubscriptionStatus _mapStripeStatus(String status) {
    switch (status) {
      case 'active':
        return ProviderSubscriptionStatus.active;
      case 'canceled':
        return ProviderSubscriptionStatus.cancelled;
      case 'incomplete':
      case 'incomplete_expired':
        return ProviderSubscriptionStatus.unpaid;
      case 'past_due':
        return ProviderSubscriptionStatus.pastDue;
      case 'trialing':
        return ProviderSubscriptionStatus.active;
      default:
        return ProviderSubscriptionStatus.unpaid;
    }
  }

  @override
  Future<PaymentResult> cancelSubscription({
    required String subscriptionId,
    bool cancelAtPeriodEnd = true,
  }) async {
    try {
      final body = {
        'cancel_at_period_end': cancelAtPeriodEnd.toString(),
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/subscriptions/$subscriptionId'),
        headers: _headers,
        body: body,
      );

      if (response.statusCode == 200) {
        return PaymentResult.success(
          paymentId: 'stripe_cancel_$subscriptionId',
          subscriptionId: subscriptionId,
        );
      } else {
        final error = json.decode(response.body);
        return PaymentResult.failure(
          error: error['error']['message'] ?? 'Failed to cancel subscription',
        );
      }
    } catch (e) {
      return PaymentResult.failure(error: 'Network error: $e');
    }
  }

  @override
  Future<PaymentResult> updateSubscription({
    required String subscriptionId,
    required SubscriptionPlan newPlan,
  }) async {
    try {
      final priceId = _priceIds[newPlan];
      if (priceId == null) {
        return PaymentResult.failure(error: 'Price ID not found for plan: ${newPlan.name}');
      }

      final body = {
        'metadata[plan]': newPlan.name,
        'proration_behavior': 'create_prorations',
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/subscriptions/$subscriptionId'),
        headers: _headers,
        body: body,
      );

      if (response.statusCode == 200) {
        return PaymentResult.success(
          paymentId: 'stripe_update_$subscriptionId',
          subscriptionId: subscriptionId,
        );
      } else {
        final error = json.decode(response.body);
        return PaymentResult.failure(
          error: error['error']['message'] ?? 'Failed to update subscription',
        );
      }
    } catch (e) {
      return PaymentResult.failure(error: 'Network error: $e');
    }
  }

  @override
  Future<PaymentCustomer?> getCustomer(String customerId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/customers/$customerId'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return PaymentCustomer(
          id: data['id'],
          email: data['email'] ?? '',
          name: data['name'] ?? '',
          provider: PaymentProvider.stripe,
          providerCustomerId: data['id'],
          metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
        );
      }
      return null;
    } catch (e) {
      print('StripeProvider: Error getting customer: $e');
      return null;
    }
  }

  @override
  Future<List<PaymentInvoice>> getCustomerInvoices(String customerId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/invoices?customer=$customerId&limit=100'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final invoices = <PaymentInvoice>[];

        for (final invoice in data['data']) {
          invoices.add(PaymentInvoice(
            id: invoice['id'],
            customerId: invoice['customer'],
            amount: (invoice['amount_paid'] ?? 0) / 100.0,
            currency: invoice['currency'] ?? 'usd',
            status: PaymentStatus.completed,
            provider: PaymentProvider.stripe,
            createdAt: DateTime.fromMillisecondsSinceEpoch(invoice['created'] * 1000),
          ));
        }

        return invoices;
      }
      return [];
    } catch (e) {
      print('StripeProvider: Error getting invoices: $e');
      return [];
    }
  }

  @override
  bool verifyWebhookSignature({
    required String payload,
    required String signature,
    required String secret,
  }) {
    try {
      final elements = signature.split(',');
      final timestamp = elements
          .firstWhere((element) => element.startsWith('t='))
          .substring(2);
      final v1Signature = elements
          .firstWhere((element) => element.startsWith('v1='))
          .substring(3);

      final signedPayload = '$timestamp.$payload';
      final expectedSignature = Hmac(sha256, utf8.encode(secret))
          .convert(utf8.encode(signedPayload))
          .toString();

      return expectedSignature == v1Signature;
    } catch (e) {
      print('StripeProvider: Webhook signature verification failed: $e');
      return false;
    }
  }

  @override
  Future<PaymentResult> processWebhookEvent(WebhookEvent event) async {
    try {
      print('StripeProvider: Processing webhook event: ${event.eventType}');

      switch (event.eventType) {
        case 'customer.subscription.created':
          return _handleSubscriptionCreated(event.data);
        case 'customer.subscription.updated':
          return _handleSubscriptionUpdated(event.data);
        case 'customer.subscription.deleted':
          return _handleSubscriptionDeleted(event.data);
        case 'invoice.payment_succeeded':
          return _handlePaymentSucceeded(event.data);
        case 'invoice.payment_failed':
          return _handlePaymentFailed(event.data);
        default:
          print('StripeProvider: Unhandled webhook event: ${event.eventType}');
          return PaymentResult.success(paymentId: 'webhook_${event.id}');
      }
    } catch (e) {
      print('StripeProvider: Error processing webhook: $e');
      return PaymentResult.failure(error: 'Webhook processing failed: $e');
    }
  }

  Future<PaymentResult> _handleSubscriptionCreated(Map<String, dynamic> data) async {
    final subscriptionId = data['id'];
    final customerId = data['customer'];
    final status = data['status'];

    print('StripeProvider: Subscription created - ID: $subscriptionId, Status: $status');

    return PaymentResult.success(
      paymentId: 'stripe_sub_$subscriptionId',
      subscriptionId: subscriptionId,
      customerId: customerId,
    );
  }

  Future<PaymentResult> _handleSubscriptionUpdated(Map<String, dynamic> data) async {
    final subscriptionId = data['id'];
    final status = data['status'];

    print('StripeProvider: Subscription updated - ID: $subscriptionId, Status: $status');

    return PaymentResult.success(
      paymentId: 'stripe_sub_update_$subscriptionId',
      subscriptionId: subscriptionId,
    );
  }

  Future<PaymentResult> _handleSubscriptionDeleted(Map<String, dynamic> data) async {
    final subscriptionId = data['id'];

    print('StripeProvider: Subscription deleted - ID: $subscriptionId');

    return PaymentResult.success(
      paymentId: 'stripe_sub_cancel_$subscriptionId',
      subscriptionId: subscriptionId,
    );
  }

  Future<PaymentResult> _handlePaymentSucceeded(Map<String, dynamic> data) async {
    final subscriptionId = data['subscription'];
    final customerId = data['customer'];
    final amountPaid = data['amount_paid'];

    print('StripeProvider: Payment succeeded - Subscription: $subscriptionId, Amount: $amountPaid');

    return PaymentResult.success(
      paymentId: 'stripe_payment_${data['id']}',
      subscriptionId: subscriptionId,
      customerId: customerId,
    );
  }

  Future<PaymentResult> _handlePaymentFailed(Map<String, dynamic> data) async {
    final subscriptionId = data['subscription'];

    print('StripeProvider: Payment failed - Subscription: $subscriptionId');

    return PaymentResult.failure(
      error: 'Payment failed for subscription: $subscriptionId',
    );
  }

  @override
  String getCheckoutUrl({
    required SubscriptionPlan plan,
    required String customerEmail,
    String? customerId,
    Map<String, dynamic> metadata = const {},
  }) {
    final priceId = _priceIds[plan];
    if (priceId == null) {
      throw ArgumentError('Price ID not found for plan: ${plan.name}');
    }

    final baseUrl = _config.isTestMode
        ? 'https://checkout.stripe.com/pay'
        : 'https://checkout.stripe.com/pay';

    final params = {
      'price': priceId,
      'customer_email': customerEmail,
      if (customerId != null) 'customer': customerId,
    };

    final queryString = params.entries
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');

    return '$baseUrl?$queryString';
  }

  @override
  String? getCustomerPortalUrl(String customerId) {
    return 'https://billing.stripe.com/p/login/test_$customerId';
  }

  @override
  bool validateConfig(PaymentConfig config) {
    return config.secretKey?.isNotEmpty == true &&
           config.webhookSecret?.isNotEmpty == true &&
           config.planIds.containsKey(SubscriptionPlan.basic) &&
           config.planIds.containsKey(SubscriptionPlan.premium) &&
           config.planIds[SubscriptionPlan.basic]?.isNotEmpty == true &&
           config.planIds[SubscriptionPlan.premium]?.isNotEmpty == true;
  }

  @override
  String get providerName => 'Stripe';

  @override
  List<String> get supportedFeatures => [
        'subscriptions',
        'one-time-payments',
        'webhooks',
        'customer-portal',
        'payment-methods',
        'invoices',
        'proration',
      ];
}
