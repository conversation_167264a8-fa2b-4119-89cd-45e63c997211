import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flight_fear_wellness_app/blocs/auth/auth_bloc.dart';
import 'package:flight_fear_wellness_app/blocs/chat/chat_bloc.dart';
import 'package:flight_fear_wellness_app/blocs/voice/voice_bloc.dart';
import 'package:flight_fear_wellness_app/repositories/auth_repository.dart';
import 'package:flight_fear_wellness_app/repositories/chat_repository.dart';
import 'package:flight_fear_wellness_app/repositories/voice_repository.dart';
import 'package:flight_fear_wellness_app/screens/auth/login_screen.dart';
import 'package:flight_fear_wellness_app/screens/main_app_screen.dart';
import 'package:flight_fear_wellness_app/services/elevenlabs_service.dart';
import 'package:flight_fear_wellness_app/services/firebase_service.dart';
import 'package:flight_fear_wellness_app/services/secure_storage_service.dart';
import 'package:flight_fear_wellness_app/utils/theme.dart';
import 'package:flight_fear_wellness_app/blocs/auth/auth_event.dart';
import 'package:flight_fear_wellness_app/blocs/auth/auth_state.dart';
import 'package:flight_fear_wellness_app/utils/constants.dart';
import 'package:flight_fear_wellness_app/screens/splashscreen/FearFlightSplashScreen.dart';
import 'package:flight_fear_wellness_app/providers/subscription_provider.dart';
import 'package:flight_fear_wellness_app/utils/admin_guard.dart';
import 'package:flight_fear_wellness_app/screens/admin/admin_dashboard.dart';
import 'package:flight_fear_wellness_app/blocs/admin/admin_bloc.dart';
import 'package:flight_fear_wellness_app/blocs/admin/admin_event.dart';
import 'package:flight_fear_wellness_app/blocs/notification/notification_bloc.dart';
import 'package:flight_fear_wellness_app/services/active_session_service.dart';
import 'package:flight_fear_wellness_app/services/payment_config_service.dart';
import 'package:flight_fear_wellness_app/services/webhook_processor.dart';




// Purpose: Initialize Firebase, load environment variables, and start the Flutter application
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load environment variables from .env file
  try {
    await dotenv.load(fileName: ".env");
    print('✅ Environment variables loaded successfully');
  } catch (e) {
    print('⚠️ Warning: Could not load .env file: $e');
    print('📝 App will use fallback values from secure storage or constants');
  }

  await Firebase.initializeApp();

  // Clear any old test configurations (for clean handover)
  await PaymentConfigService.clearAllStoredConfigurations();

  // OPTION 1: Configure payment providers programmatically (uncomment to use)
  // await PaymentConfigService.setupProductionEnvironment();

  // OPTION 2: Load payment configs from secure storage (admin UI configurations)
  await PaymentConfigService.initializeFromStorage();

  await WebhookProcessor().initialize();

  runApp(const MyApp());
}

// Purpose: Root widget that sets up providers, blocs, and app theme
class MyApp extends StatelessWidget {
  const MyApp({super.key});


  // Purpose: Build the app with all necessary providers and navigation setup
   @override
  Widget build(BuildContext context) {
    return RepositoryProvider(
      create: (context) => AuthRepository(
        firebaseService: FirebaseService(),
      ),
      child: MultiProvider(
        providers: [

          ChangeNotifierProvider(
            create: (context) => SubscriptionProvider(),
          ),
        ],
        child: MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (context) => AuthBloc(
                authRepository: context.read<AuthRepository>(),
              )..add(CheckAuthenticationStatus()),),
            BlocProvider(
              create: (context) => ChatBloc(
                chatRepository: ChatRepository(
                  firebaseService: FirebaseService(),
                  subscriptionProvider: context.read<SubscriptionProvider>(),
                ),
              ),
            ),

           BlocProvider(
              create: (context) => VoiceBloc(
                voiceRepository: VoiceRepository(
                  elevenLabsService: ElevenLabsService(),
                  subscriptionProvider: context.read<SubscriptionProvider>(),
                ),
              ),
            ),
            BlocProvider(
              create: (context) => AdminBloc(),
            ),
            BlocProvider(
              create: (context) => NotificationBloc(),
            ),
          ],
          child: MaterialApp(
              title: AppConstants.appName,
              theme: AppTheme.lightTheme,
              debugShowCheckedModeBanner: false,

              home: const AuthWrapper(),
            ),
          ),
        ),
    );
  }
}




// Purpose: Handle authentication state and navigation between login/main app
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}


// Purpose: Monitor app lifecycle changes for session management
class AppLifecycleObserver extends WidgetsBindingObserver {
  // Purpose: Track app state changes for session management
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
        SecureStorageService.markAppClosed();
        break;
      case AppLifecycleState.resumed:
        break;
      default:
        break;
    }
  }
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _shouldShowSplash = true;
  bool _splashCompleted = false;
  bool _sessionStateLoaded = false;
  late AppLifecycleObserver _lifecycleObserver;
  AuthStatus? _lastAuthStatus;
  final ActiveSessionService _sessionService = ActiveSessionService();

  @override
  void initState() {
    super.initState();
    _lifecycleObserver = AppLifecycleObserver();
    WidgetsBinding.instance.addObserver(_lifecycleObserver);
    _initializeApp();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(_lifecycleObserver);
    _sessionService.endSession();
    _sessionService.dispose();
    super.dispose();
  }

  // Purpose: Initialize app session state and determine if splash screen should be shown
  Future<void> _initializeApp() async {
    final sessionState = await SecureStorageService.getAppSessionState();
    final wasRecentlyClosed = await SecureStorageService.wasAppRecentlyClosed();


    bool shouldShowSplash = false;

    if (sessionState == AppSessionState.freshStart) {
      shouldShowSplash = true;
      await SecureStorageService.setAppSessionState(AppSessionState.logoutLogin);
    } else if (!wasRecentlyClosed) {
      shouldShowSplash = true;
      await SecureStorageService.setAppSessionState(AppSessionState.logoutLogin);
    }

    setState(() {
      _shouldShowSplash = shouldShowSplash;
      _sessionStateLoaded = true;
    });


    if (_shouldShowSplash) {
      _startSplashSequence();
    } else {
      if (mounted) {
        context.read<AuthBloc>().add(CheckAuthenticationStatus());
      }
    }
  }

  // Purpose: Start splash screen sequence and authentication check
  Future<void> _startSplashSequence() async {
    if (mounted) {
      context.read<AuthBloc>().add(CheckAuthenticationStatus());
    }


  }

  // Purpose: Handle splash screen completion callback
  void onSplashCompleted() {
    if (mounted) {
      setState(() {
        _splashCompleted = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(

      buildWhen: (previous, current) {
        print('AuthWrapper: State change from ${previous.status} to ${current.status}');
        return true;
      },
      builder: (context, state) {
        print('AuthWrapper: Building with auth status = ${state.status}, user = ${state.user?.email}');


        _handleSessionTracking(state.status);


        if (((_shouldShowSplash && !_splashCompleted) || !_sessionStateLoaded) &&
            state.status != AuthStatus.authenticated) {
          print('AuthWrapper: Showing splash screen');
          return FearFlightSplashScreen(
            onLoadingComplete: onSplashCompleted,
          );
        }


        if (state.status == AuthStatus.authenticated && state.user != null) {
          print('AuthWrapper: User authenticated: ${state.user!.email}');


          if (_shouldShowSplash || !_splashCompleted) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                setState(() {
                  _shouldShowSplash = false;
                  _splashCompleted = true;
                });
              }
            });
          }


          final isAdmin = AdminGuard.isAdminEmail(state.user!.email);

          if (isAdmin) {
            print('AuthWrapper: Admin user authenticated, showing AdminDashboard');
            return const AdminDashboard();
          } else {

            print('AuthWrapper: Non-admin user detected, resetting admin state');
            if (mounted) {
              try {
                context.read<AdminBloc>().add(ResetAdminState());
              } catch (e) {
                print('AuthWrapper: Error resetting admin state: $e');
              }
            }

            print('AuthWrapper: Regular user authenticated, showing MainAppScreen');
            return const MainAppScreen();
          }
        }


        print('AuthWrapper: User not authenticated, showing LoginScreen');
        return const LoginScreen();
      },
    );
  }

  // Purpose: Track user session state changes for analytics and security
  void _handleSessionTracking(AuthStatus currentStatus) {
    if (_lastAuthStatus == currentStatus) return;

    if (currentStatus == AuthStatus.authenticated && _lastAuthStatus != AuthStatus.authenticated) {
      _sessionService.startSession();
    } else if (currentStatus != AuthStatus.authenticated && _lastAuthStatus == AuthStatus.authenticated) {
      _sessionService.endSession();


      if (mounted) {
        context.read<AdminBloc>().add(ResetAdminState());
      }
    }

    _lastAuthStatus = currentStatus;
  }
}
