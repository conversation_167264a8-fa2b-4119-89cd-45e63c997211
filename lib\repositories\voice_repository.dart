import 'dart:async';
import 'dart:io';
import 'package:audioplayers/audioplayers.dart';
import 'package:flight_fear_wellness_app/models/api_response.dart';
import 'package:flight_fear_wellness_app/models/chat_message.dart';
import 'package:flight_fear_wellness_app/services/elevenlabs_service.dart';
import 'package:flight_fear_wellness_app/services/secure_api_service.dart';
import 'package:flight_fear_wellness_app/services/crisis_intervention_service.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flight_fear_wellness_app/providers/subscription_provider.dart';

class VoiceRepository {
  final ElevenLabsService elevenLabsService;
  final AudioPlayer audioPlayer = AudioPlayer();
  final SecureAPIService _secureAPIService = SecureAPIService();
  final SubscriptionProvider? subscriptionProvider;

  VoiceRepository({
    required this.elevenLabsService,
    this.subscriptionProvider,
  });

  Future<MoodBasedVoiceResponse> generateVoiceAIResponse(
    String userId,
    String input,
    {List<ChatMessage>? conversationHistory}
  ) async {
    try {
      final historyForAPI = conversationHistory?.map((msg) => {
        'role': msg.type == MessageType.user ? 'user' : 'assistant',
        'content': msg.content,
      }).toList() ?? [];

      final response = await _secureAPIService.processVoiceChatRequest(
        message: input,
        conversationHistory: historyForAPI,
      );

      if (!response['success']) {
        throw Exception(response['error'] ?? 'Failed to generate AI response');
      }

      final aiResponse = response['message'];
      final moodLevel = response['moodLevel'] ?? 'normal';

      if (subscriptionProvider != null && response['updatedCredits'] != null) {
        print('VoiceRepository: Updating subscription provider with chat credit data');
        print('VoiceRepository: Chat credit data received: ${response['updatedCredits']}');
        subscriptionProvider!.updateCreditsDirectly(response['updatedCredits']);
      } else {
        print('VoiceRepository: Warning - No subscription provider or chat credit data available');
      }

      final interventionOptions = CrisisInterventionService.getCrisisOptions(moodLevel);

      final shouldOfferDebate = CrisisInterventionService.shouldOfferDebate(input, historyForAPI);

      return MoodBasedVoiceResponse(
        message: aiResponse,
        moodLevel: moodLevel,
        interventionOptions: interventionOptions,
        shouldOfferDebate: shouldOfferDebate,
        conversationId: DateTime.now().millisecondsSinceEpoch.toString(),
        tokensUsed: response['usage']?['total_tokens'] ?? 0,
      );
    } catch (e) {
      throw Exception('Voice AI response failed: $e');
    }
  }

  Future<ChatResponse> getAIResponse(String userId, String input, {List<ChatMessage>? conversationHistory}) async {
    try {
      final historyForAPI = conversationHistory?.map((msg) => {
        'role': msg.type == MessageType.user ? 'user' : 'assistant',
        'content': msg.content,
      }).toList() ?? [];

      final response = await _secureAPIService.processChatRequest(
        message: input,
        conversationHistory: historyForAPI,
      );

      if (!response['success']) {
        throw Exception(response['error'] ?? 'Failed to generate AI response');
      }

      if (subscriptionProvider != null && response['updatedCredits'] != null) {
        print('VoiceRepository: Updating subscription provider with chat credit data');
        print('VoiceRepository: Chat credit data received: ${response['updatedCredits']}');
        subscriptionProvider!.updateCreditsDirectly(response['updatedCredits']);
      } else {
        print('VoiceRepository: Warning - No subscription provider or chat credit data available');
      }

      return ChatResponse(
        message: response['message'],
        conversationId: DateTime.now().millisecondsSinceEpoch.toString(),
        tokensUsed: response['usage']?['total_tokens'] ?? 0,
      );
    } catch (e) {
      throw Exception('Voice AI response failed: $e');
    }
  }

  static Directory? _cachedDirectory;

  Future<VoiceResponse> getVoiceResponseOptimized(String userId, String input, {List<ChatMessage>? conversationHistory}) async {
    try {
      final startTime = DateTime.now();

      _cachedDirectory ??= await getTemporaryDirectory();
      final filePath = '${_cachedDirectory!.path}/voice_${DateTime.now().millisecondsSinceEpoch}.mp3';

      final historyForAPI = conversationHistory?.map((msg) => {
        'role': msg.type == MessageType.user ? 'user' : 'assistant',
        'content': msg.content,
      }).toList() ?? [];

      final response = await _secureAPIService.processVoiceCombinedRequest(
        message: input,
        conversationHistory: historyForAPI,
      );

      if (!response['success']) {
        throw Exception(response['error'] ?? 'Voice generation failed');
      }

      final audioData = response['audioData'] as List<int>;
      final file = File(filePath);
      await file.writeAsBytes(audioData, flush: true);

      final totalTime = DateTime.now().difference(startTime).inMilliseconds;
      print('🚀 VOICE REPOSITORY TIME: ${totalTime}ms');

      if (subscriptionProvider != null && response['updatedCredits'] != null) {
        subscriptionProvider!.updateCreditsDirectly(response['updatedCredits']);
      }

      return VoiceResponse(
        message: response['message'],
        audioFilePath: file.path,
        conversationId: DateTime.now().millisecondsSinceEpoch.toString(),
        tokensUsed: response['usage']?['total_tokens'] ?? 0,
        characters: response['characters'] ?? 0,
      );
    } catch (e) {
      throw Exception('Voice failed: ${e.toString().replaceAll('Exception: ', '')}');
    }
  }

  Future<String> textToSpeech(String text) async {
    try {
      final response = await _secureAPIService.processVoiceRequest(text: text);

      if (!response['success']) {
        throw Exception(response['error'] ?? 'Failed to generate voice');
      }

      final audioData = response['audioData'] as List<int>;
      final directory = await getTemporaryDirectory();
      final file = File('${directory.path}/voice_${DateTime.now().millisecondsSinceEpoch}.mp3');
      await file.writeAsBytes(audioData);

      if (subscriptionProvider != null && response['updatedCredits'] != null) {
        print('VoiceRepository: Updating subscription provider with new credit data');
        print('VoiceRepository: Credit data received: ${response['updatedCredits']}');
        subscriptionProvider!.updateCreditsDirectly(response['updatedCredits']);
      } else {
        print('VoiceRepository: Warning - No subscription provider or credit data available');
        if (subscriptionProvider == null) {
          print('VoiceRepository: Subscription provider is null');
        }
        if (response['updatedCredits'] == null) {
          print('VoiceRepository: Updated credits data is null');
        }
      }

      return file.path;
    } catch (e) {
      throw Exception('Voice generation failed: $e');
    }
  }

  Future<void> playAudio(String filePath) async {
    try {
      audioPlayer.stop();

      await Future.wait([
        audioPlayer.setVolume(1.0),
        audioPlayer.setPlaybackRate(1.0),
      ]);

      await audioPlayer.play(DeviceFileSource(filePath));
    } catch (e) {
      throw Exception('Audio playback failed: $e');
    }
  }

  Future<void> stopAudio() async {
    try {
      await audioPlayer.stop();
    } catch (e) {
    }
  }

  Future<Duration?> getAudioDuration(String filePath) async {
    try {
      await audioPlayer.setSource(DeviceFileSource(filePath));
      return await audioPlayer.getDuration();
    } catch (e) {
      return null;
    }
  }
}