name: flight_fear_wellness_app
description: "AI Mental Wellness App for Flight Fear"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.1+1

environment:
  sdk: ">=3.0.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  
  # Firebase
  firebase_core: ^2.27.0
  firebase_auth: ^4.19.1
  cloud_firestore: ^4.15.2
  firebase_database: ^10.4.0
  
  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  provider: ^6.1.1
  cloud_functions: ^4.5.4
  flutter_dotenv: ^5.1.0
  
  
  # HTTP & API
  http: ^1.1.2
  dio: ^5.4.0

  # Payment & Crypto
  crypto: ^3.0.3
  url_launcher: ^6.3.2
  
  # Audio & Speech
  flutter_speech: ^2.0.1  # Compatible speech recognition
  flutter_tts: ^3.8.5
  permission_handler: ^11.4.0  # For microphone permissions
  just_audio: ^0.9.36
  audioplayers: ^6.5.0
 
 
  
  # UI & Navigation
  cupertino_icons: ^1.0.2
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  
  # Utilities
  shared_preferences: ^2.2.2
  path_provider: ^2.1.1
  uuid: ^4.2.1
  intl: ^0.19.0
  sentry_flutter: ^7.15.0
  dartz: ^0.10.1
  vector_math: ^2.1.4
  bloc: ^8.1.4
  image_picker: ^1.0.4
  shimmer: ^3.0.0
  firebase_storage: ^11.7.7
  timeago: ^3.7.1

  

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # App Icon Generator
  flutter_launcher_icons: ^0.13.1

dependency_overrides:

# Dependency overrides removed - flutter_unity_widget disabled

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/images/
    - .env
  
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# App Icon Configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/Alora_Icon.png"
  min_sdk_android: 21
  remove_alpha_ios: true