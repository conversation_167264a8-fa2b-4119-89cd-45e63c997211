import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:flight_fear_wellness_app/repositories/voice_repository.dart';
import 'package:flight_fear_wellness_app/services/secure_api_service.dart';
import 'package:flight_fear_wellness_app/services/speech_recognition_service.dart';
import 'package:flight_fear_wellness_app/services/subscription_service.dart';
import 'package:flight_fear_wellness_app/models/chat_message.dart';
import 'voice_event.dart';
import 'voice_state.dart';

class VoiceBloc extends Bloc<VoiceEvent, VoiceState> {
  final VoiceRepository voiceRepository;
  final SpeechRecognitionService speechService = SpeechRecognitionService();
  String? recognitionResult;
  Timer? _recordingTimer;
  Timer? _performanceTimer;
  Timer? _textAnimationTimer;
  StreamSubscription? _speechResultSubscription;
  StreamSubscription? _speechErrorSubscription;

  final List<ChatMessage> _conversationHistory = [];
  DateTime? _lastInteractionTime;

  VoiceBloc({required this.voiceRepository}) : super(const VoiceState()) {
    on<StartRecordingEvent>(_onStartRecording);
    on<StopRecordingEvent>(_onStopRecording);
    on<ProcessVoiceInputEvent>(_onProcessVoiceInput);
    on<PlayAudioEvent>(_onPlayAudio);
    on<StopAudioEvent>(_onStopAudio);
    on<StartTextAnimationEvent>(_onStartTextAnimation);
    on<UpdateDisplayedTextEvent>(_onUpdateDisplayedText);
    on<StopTextAnimationEvent>(_onStopTextAnimation);
    on<ResetVoiceSessionEvent>(_onReset);
    on<UpdateSpeechResultEvent>(_onUpdateSpeechResult);
    on<SpeechErrorEvent>(_onSpeechError);
    on<TriggerVoiceCrisisInterventionEvent>(_onTriggerVoiceCrisisIntervention);
    on<CloseVoiceCrisisInterventionEvent>(_onCloseVoiceCrisisIntervention);
    on<ReturnFromVoiceBreathingExerciseEvent>(_onReturnFromVoiceBreathingExercise);

      _speechResultSubscription = speechService.onResult.listen((result) {
      if (result.startsWith("SPEECH_COMPLETED:")) {
        final finalText = result.substring("SPEECH_COMPLETED:".length);

        recognitionResult = finalText;
        if (state.isRecording && finalText.isNotEmpty) {
          add(StopRecordingEvent(''));
        }
      } else if (result.isNotEmpty) {

        recognitionResult = result;
        add(UpdateSpeechResultEvent(result));
      }
    });

    _performanceTimer = Timer.periodic(Duration(seconds: 30), (_) {
      SecureAPIService.cleanupRateLimitData();
    });
    _speechErrorSubscription = speechService.onError.listen((error) {
      add(SpeechErrorEvent(error));
    });
  }

  // Purpose: Start voice recording and speech recognition
  Future<void> _onStartRecording(
    StartRecordingEvent event,
    Emitter<VoiceState> emit,
  ) async {
    if (!state.isRecording) {
      try {
        final subscriptionService = SubscriptionService();
        final hasVoiceCredits = await subscriptionService.canUseVoice();

        if (!hasVoiceCredits) {
          emit(state.copyWith(
            status: VoiceStatus.error,
            error: 'Insufficient voice credits. Please upgrade your plan to continue using voice features.',
          ));
          return;
        }

        speechService.resetAvailability();

        final isAvailable = await speechService.initialize();

        if (isAvailable) {
          emit(state.copyWith(isRecording: true, status: VoiceStatus.recording));

          final success = await speechService.startListening(
            listenFor: const Duration(seconds: 45),
            pauseFor: const Duration(seconds: 5),
            partialResults: true,
          );

          if (success) {
            _recordingTimer = Timer(const Duration(seconds: 45), () {
              add(StopRecordingEvent(event.userId));
            });
          } else {
            emit(state.copyWith(
              status: VoiceStatus.error,
              error: 'Failed to start speech recognition',
              isRecording: false,
            ));
          }
        } else {
          emit(state.copyWith(
            status: VoiceStatus.error,
            error: 'Speech recognition not available. Please check microphone permissions.',
          ));
        }
      } catch (e) {
        emit(state.copyWith(
          status: VoiceStatus.error,
          error: 'Unable to check voice credits. Please try again.',
        ));
      }
    }
  }

  // Purpose: Stop voice recording and process recognized speech
  Future<void> _onStopRecording(
    StopRecordingEvent event,
    Emitter<VoiceState> emit,
  ) async {
    if (state.isRecording) {
      await Future.delayed(const Duration(milliseconds: 200));

      final result = await speechService.stopListening();
      _recordingTimer?.cancel();

      String finalResult = '';

      if (result != null && result.isNotEmpty && recognitionResult != null && recognitionResult!.isNotEmpty) {
        final resultWords = result.trim().split(' ').length;
        final recognitionWords = recognitionResult!.trim().split(' ').length;

        if (resultWords >= recognitionWords) {
          finalResult = result;
        } else {
          finalResult = recognitionResult!;
        }
      } else {
        finalResult = result ?? recognitionResult ?? '';
      }

      if (finalResult.isNotEmpty && finalResult.trim().length > 1) {
        final processedInput = _handlePotentialInterruption(finalResult);

        emit(state.copyWith(
          isRecording: false,
          currentInput: processedInput,
          status: VoiceStatus.processing,
        ));

        add(ProcessVoiceInputEvent(
          userId: event.userId,
          input: processedInput,
        ));
      } else {
        emit(state.copyWith(
          isRecording: false,
          status: VoiceStatus.idle,
          error: 'No clear speech detected. Please speak clearly and try again.',
        ));
      }

      recognitionResult = null;
    }
  }

      // Purpose: Process voice input through AI and generate audio response
      Future<void> _onProcessVoiceInput(
      ProcessVoiceInputEvent event,
      Emitter<VoiceState> emit,
    ) async {
      try {
        final processingStart = DateTime.now();
        emit(state.copyWith(
          status: VoiceStatus.processing,
          currentInput: event.input,
        ));

        final now = DateTime.now();
        final isFirstInteraction = _conversationHistory.isEmpty ||
            (_lastInteractionTime != null &&
             now.difference(_lastInteractionTime!).inHours >= 3);

        _conversationHistory.add(ChatMessage(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          content: event.input,
          type: MessageType.user,
          timestamp: DateTime.now(),
          userId: event.userId,
        ));

        final moodResponse = await voiceRepository.generateVoiceAIResponse(
          event.userId,
          event.input,
          conversationHistory: _conversationHistory,
        );

        _conversationHistory.add(ChatMessage(
          id: (DateTime.now().millisecondsSinceEpoch + 1).toString(),
          content: moodResponse.message,
          type: MessageType.ai,
          timestamp: DateTime.now(),
          userId: 'alora',
        ));

        if (_conversationHistory.length > 10) {
          _conversationHistory.removeRange(0, _conversationHistory.length - 10);
        }

        _lastInteractionTime = now;

        final voiceResponse = await voiceRepository.getVoiceResponseOptimized(
          event.userId,
          moodResponse.message
        );

        final processingTime = DateTime.now().difference(processingStart).inMilliseconds;
        print('🚀 TOTAL PROCESSING TIME: ${processingTime}ms');

        emit(state.copyWith(
          currentResponse: voiceResponse.message,
          audioFilePath: voiceResponse.audioFilePath,
          displayedText: voiceResponse.message,
          status: VoiceStatus.playing,
          isAnimatingText: true,
          crisisOptions: moodResponse.interventionOptions,
          currentMoodLevel: moodResponse.moodLevel,
          shouldOfferDebate: moodResponse.shouldOfferDebate,
          error: null,
          currentInput: event.input,
        ));

        voiceRepository.playAudio(voiceResponse.audioFilePath).then((_) {
          if (state.status == VoiceStatus.playing) {
            add(StopTextAnimationEvent());
          }
        }).catchError((e) {
          print('Audio playback error: $e');
          add(StopTextAnimationEvent());
        });

      } catch (e) {
        emit(state.copyWith(
          status: VoiceStatus.error,
          error: 'Failed to process: ${e.toString().replaceAll('Exception: ', '')}',
          isAnimatingText: false,
        ));
      }
    }

  String _handlePotentialInterruption(String input) {
    final trimmedInput = input.trim();

    final wordCount = trimmedInput.split(' ').length;

    if (wordCount <= 2 &&
        !trimmedInput.endsWith('.') &&
        !trimmedInput.endsWith('?') &&
        !trimmedInput.endsWith('!')) {

      final obviousIncompletePatterns = [
        RegExp(r'\b(I|I am)\s*$', caseSensitive: false),
        RegExp(r'\b(the|a|an)\s*$', caseSensitive: false),
        RegExp(r'\b(but|and)\s*$', caseSensitive: false),
      ];

      bool seemsIncomplete = false;
      for (final pattern in obviousIncompletePatterns) {
        if (pattern.hasMatch(trimmedInput)) {
          seemsIncomplete = true;
          break;
        }
      }

      if (seemsIncomplete) {
        return '$trimmedInput [SPEECH_INTERRUPTED: User may have stopped mid-sentence or had difficulty completing their thought. Respond empathetically and ask if they wanted to say something else or if they\'re okay.]';
      }
    }

    return trimmedInput;
  }

  Duration _estimateAudioDuration(String text) {
    final wordCount = text.split(' ').length;
    final estimatedSeconds = (wordCount / 2.5).ceil();
    return Duration(seconds: estimatedSeconds + 1);
  }

  // Purpose: Play generated audio response to user
  Future<void> _onPlayAudio(
    PlayAudioEvent event,
    Emitter<VoiceState> emit,
  ) async {
    try {
      emit(state.copyWith(status: VoiceStatus.playing));
      await voiceRepository.playAudio(event.filePath);
      emit(state.copyWith(status: VoiceStatus.idle));
    } catch (e) {
      emit(state.copyWith(
        status: VoiceStatus.error,
        error: 'Failed to play audio: $e',
      ));
    }
  }

  // Purpose: Stop audio playback and reset audio state
  Future<void> _onStopAudio(
    StopAudioEvent event,
    Emitter<VoiceState> emit,
  ) async {
    try {
      await voiceRepository.stopAudio();
      _textAnimationTimer?.cancel();
      emit(state.copyWith(
        status: VoiceStatus.idle,
        isAnimatingText: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: VoiceStatus.error,
        error: 'Failed to stop audio: $e',
      ));
    }
  }

  Future<void> _onStartTextAnimation(
    StartTextAnimationEvent event,
    Emitter<VoiceState> emit,
  ) async {
    try {
      emit(state.copyWith(
        status: VoiceStatus.playing,
        isAnimatingText: true,
        displayedText: event.text,
      ));

      voiceRepository.playAudio(event.audioFilePath);

      final estimatedDuration = _estimateAudioDuration(event.text);
      _textAnimationTimer = Timer(estimatedDuration, () {
        if (state.isAnimatingText) {
          add(StopTextAnimationEvent());
        }
      });

    } catch (e) {
      emit(state.copyWith(
        status: VoiceStatus.error,
        error: 'Failed to start text animation: $e',
        isAnimatingText: false,
      ));
    }
  }

  Future<void> _onUpdateDisplayedText(
    UpdateDisplayedTextEvent event,
    Emitter<VoiceState> emit,
  ) async {
  }

  Future<void> _onStopTextAnimation(
    StopTextAnimationEvent event,
    Emitter<VoiceState> emit,
  ) async {
    _textAnimationTimer?.cancel();

    emit(state.copyWith(
      displayedText: state.currentResponse ?? '',
      isAnimatingText: false,
      status: VoiceStatus.idle,
    ));
  }

  Future<void> _onReset(
    ResetVoiceSessionEvent event,
    Emitter<VoiceState> emit,
  ) async {
    try {
      await voiceRepository.stopAudio();
      _textAnimationTimer?.cancel();
      recognitionResult = null;

      _conversationHistory.clear();
      _lastInteractionTime = null;

      emit(const VoiceState());
    } catch (e) {
      emit(state.copyWith(
        status: VoiceStatus.error,
        error: 'Failed to reset voice session: $e',
      ));
    }
  }

  Future<void> _onUpdateSpeechResult(
    UpdateSpeechResultEvent event,
    Emitter<VoiceState> emit,
  ) async {
    emit(state.copyWith(currentInput: event.result));
  }

  Future<void> _onSpeechError(
    SpeechErrorEvent event,
    Emitter<VoiceState> emit,
  ) async {
    if (event.error.toLowerCase().contains('no speech') ||
        event.error.toLowerCase().contains('no match') ||
        event.error.toLowerCase().contains('timeout')) {
      emit(state.copyWith(
        status: VoiceStatus.idle,
        isRecording: false,
        currentInput: null,
      ));
    } else {
      emit(state.copyWith(
        status: VoiceStatus.error,
        error: event.error,
        isRecording: false,
      ));
    }
  }

  void _onTriggerVoiceCrisisIntervention(
    TriggerVoiceCrisisInterventionEvent event,
    Emitter<VoiceState> emit,
  ) {
    if (event.moodLevel == 'close') {
      emit(state.copyWith(
        crisisOptions: [],
      ));
    }
  }

  void _onCloseVoiceCrisisIntervention(
    CloseVoiceCrisisInterventionEvent event,
    Emitter<VoiceState> emit,
  ) {
    emit(state.copyWith(
      crisisOptions: [],
    ));
  }

  void _onReturnFromVoiceBreathingExercise(
    ReturnFromVoiceBreathingExerciseEvent event,
    Emitter<VoiceState> emit,
  ) {
    emit(state.copyWith(
      crisisOptions: [],
    ));
  }

  @override
  Future<void> close() {
    speechService.stopListening();
    _speechResultSubscription?.cancel();
    _speechErrorSubscription?.cancel();
    _recordingTimer?.cancel();
    _textAnimationTimer?.cancel();
    voiceRepository.stopAudio();
    speechService.dispose();
    return super.close();
  }
}