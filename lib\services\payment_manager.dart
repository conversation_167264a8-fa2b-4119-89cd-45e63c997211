import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/payment_models.dart';
import '../models/subscription_model.dart';
import '../interfaces/payment_provider_interface.dart';
import 'payment_factory.dart';
import 'secure_storage_service.dart';

class PaymentManager {
  static PaymentManager? _instance;
  static PaymentManager get instance => _instance ??= PaymentManager._();
  PaymentManager._();

  final Map<PaymentProvider, PaymentProviderInterface> _providers = {};
  final Map<PaymentProvider, PaymentConfig> _configs = {};
  PaymentProvider? _primaryProvider;
  bool _isInitialized = false;

  Future<void> initialize({
    required Map<PaymentProvider, PaymentConfig> configs,
    PaymentProvider? primaryProvider,
  }) async {
    try {
      _configs.addAll(configs);
      _primaryProvider = primaryProvider ?? configs.keys.first;

      for (final entry in configs.entries) {
        final provider = PaymentProviderFactory.create(entry.key);
        await provider.initialize(entry.value);
        _providers[entry.key] = provider;
      }

      _isInitialized = true;
      debugPrint('PaymentManager initialized with ${configs.length} providers');
    } catch (e) {
      debugPrint('Failed to initialize PaymentManager: $e');
      rethrow;
    }
  }

  Future<void> loadFromStorage() async {
    try {
      final configsJson = await SecureStorageService.getDecrypted('payment_configs');
      if (configsJson != null) {
        final configsMap = jsonDecode(configsJson) as Map<String, dynamic>;
        final configs = <PaymentProvider, PaymentConfig>{};

        for (final entry in configsMap.entries) {
          final provider = PaymentProvider.values.firstWhere(
            (p) => p.name == entry.key,
          );
          final configData = entry.value as Map<String, dynamic>;
          
          configs[provider] = PaymentConfig(
            provider: provider,
            apiKey: configData['apiKey'],
            secretKey: configData['secretKey'],
            storeId: configData['storeId'],
            webhookSecret: configData['webhookSecret'],
            isTestMode: configData['isTestMode'] ?? true,
            planIds: Map<SubscriptionPlan, String>.from(
              configData['planIds'] ?? {},
            ),
          );
        }

        if (configs.isNotEmpty) {
          await initialize(configs: configs);
        }
      }
    } catch (e) {
      debugPrint('Failed to load payment configs from storage: $e');
    }
  }

  Future<void> saveToStorage() async {
    try {
      final configsMap = <String, dynamic>{};
      
      for (final entry in _configs.entries) {
        configsMap[entry.key.name] = {
          'apiKey': entry.value.apiKey,
          'secretKey': entry.value.secretKey,
          'storeId': entry.value.storeId,
          'webhookSecret': entry.value.webhookSecret,
          'isTestMode': entry.value.isTestMode,
          'planIds': entry.value.planIds.map(
            (plan, id) => MapEntry(plan.name, id),
          ),
        };
      }

      await SecureStorageService.storeEncrypted(
        'payment_configs',
        jsonEncode(configsMap),
      );
    } catch (e) {
      debugPrint('Failed to save payment configs to storage: $e');
    }
  }

  Future<void> addProvider(PaymentConfig config) async {
    try {
      final provider = PaymentProviderFactory.create(config.provider);
      await provider.initialize(config);

      _providers[config.provider] = provider;
      _configs[config.provider] = config;

      _primaryProvider ??= config.provider;

      await saveToStorage();
      debugPrint('Added payment provider: ${config.provider.name}');
    } catch (e) {
      debugPrint('Failed to add payment provider: $e');
      rethrow;
    }
  }



  Future<void> removeProvider(PaymentProvider provider) async {
    _providers.remove(provider);
    _configs.remove(provider);

    if (_primaryProvider == provider) {
      _primaryProvider = _providers.keys.isNotEmpty ? _providers.keys.first : null;
    }

    await saveToStorage();
    debugPrint('Removed payment provider: ${provider.name}');
  }

  Future<void> clearAllConfigurations() async {
    _providers.clear();
    _configs.clear();
    _primaryProvider = null;
    _isInitialized = false;

    // Clear from storage
    await SecureStorageService.deleteEncrypted('payment_configs');
    debugPrint('All payment configurations cleared');
  }

  PaymentProviderInterface? get primaryProvider {
    if (_primaryProvider == null) return null;
    return _providers[_primaryProvider];
  }

  PaymentProvider? get primaryProviderType => _primaryProvider;

  PaymentProviderInterface? getProvider(PaymentProvider provider) {
    return _providers[provider];
  }

  void setPrimaryProvider(PaymentProvider provider) {
    if (_providers.containsKey(provider)) {
      _primaryProvider = provider;
      debugPrint('Set primary payment provider: ${provider.name}');
    }
  }

  Future<PaymentResult> createSubscription({
    required String customerEmail,
    required String customerName,
    required SubscriptionPlan plan,
    PaymentProvider? preferredProvider,
  }) async {
    final provider = preferredProvider != null 
        ? _providers[preferredProvider] 
        : primaryProvider;
        
    if (provider == null) {
      return PaymentResult.failure(error: 'No payment provider available');
    }

    try {
      final customerResult = await provider.createCustomer(
        email: customerEmail,
        name: customerName,
        metadata: {
          'plan': plan.name,
          'created_via': 'flutter_app',
        },
      );

      if (!customerResult.success) {
        return customerResult;
      }

      final subscriptionResult = await provider.createSubscription(
        customerId: customerResult.customerId!,
        plan: plan,
        metadata: {
          'customer_email': customerEmail,
          'created_via': 'flutter_app',
        },
      );

      if (subscriptionResult.success) {
        await _storePaymentInfo(
          customerEmail: customerEmail,
          provider: preferredProvider ?? _primaryProvider!,
          customerId: customerResult.customerId!,
          subscriptionId: subscriptionResult.subscriptionId!,
          plan: plan,
        );
      }

      return subscriptionResult;
    } catch (e) {
      debugPrint('Failed to create subscription: $e');
      return PaymentResult.failure(error: e.toString());
    }
  }

  String? getCheckoutUrl({
    required SubscriptionPlan plan,
    required String customerEmail,
    PaymentProvider? preferredProvider,
  }) {
    final provider = preferredProvider != null 
        ? _providers[preferredProvider] 
        : primaryProvider;
        
    if (provider == null) return null;

    return provider.getCheckoutUrl(
      plan: plan,
      customerEmail: customerEmail,
      metadata: {
        'created_via': 'flutter_app',
        'plan': plan.name,
      },
    );
  }

  Future<PaymentResult> processWebhook({
    required PaymentProvider provider,
    required String payload,
    required String signature,
    required Map<String, dynamic> headers,
  }) async {
    final providerInstance = _providers[provider];
    if (providerInstance == null) {
      return PaymentResult.failure(error: 'Provider not configured');
    }

    final config = _configs[provider];
    if (config?.webhookSecret == null) {
      return PaymentResult.failure(error: 'Webhook secret not configured');
    }

    try {
      final isValid = providerInstance.verifyWebhookSignature(
        payload: payload,
        signature: signature,
        secret: config!.webhookSecret!,
      );

      if (!isValid) {
        return PaymentResult.failure(error: 'Invalid webhook signature');
      }

      final eventData = jsonDecode(payload) as Map<String, dynamic>;
      final event = WebhookEvent(
        id: eventData['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
        provider: provider,
        eventType: eventData['type'] ?? eventData['event_type'] ?? 'unknown',
        data: eventData,
        timestamp: DateTime.now(),
        signature: signature,
      );

      return await providerInstance.processWebhookEvent(event);
    } catch (e) {
      debugPrint('Failed to process webhook: $e');
      return PaymentResult.failure(error: e.toString());
    }
  }

  Future<void> _storePaymentInfo({
    required String customerEmail,
    required PaymentProvider provider,
    required String customerId,
    required String subscriptionId,
    required SubscriptionPlan plan,
  }) async {
    try {
      debugPrint('Storing payment info for $customerEmail');


    } catch (e) {
      debugPrint('Failed to store payment info: $e');
    }
  }

  List<PaymentProvider> get availableProviders => _providers.keys.toList();

  bool get isInitialized => _isInitialized;

  PaymentConfig? getConfig(PaymentProvider provider) {
    return _configs[provider];
  }

  Map<PaymentProvider, Map<String, dynamic>> get providerInfo {
    return _configs.map((provider, config) => MapEntry(
      provider,
      {
        'name': _providers[provider]?.providerName ?? provider.name,
        'features': _providers[provider]?.supportedFeatures ?? [],
        'isTestMode': config.isTestMode,
        'hasApiKey': config.apiKey.isNotEmpty,
        'hasWebhookSecret': config.webhookSecret?.isNotEmpty ?? false,
        'planIds': config.planIds.keys.map((p) => p.name).toList(),
      },
    ));
  }
}
