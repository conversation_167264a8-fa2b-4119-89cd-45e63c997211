// Add these rules to your Firestore security rules for notifications to work

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // User notifications - users can read/write their own notifications
    match /user_notifications/{notificationId} {
      allow read, write: if request.auth != null && 
        (resource == null || resource.data.userId == request.auth.uid);
    }
    
    // Alternative: If you're using 'notifications' collection instead
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null && 
        (resource == null || resource.data.userId == request.auth.uid);
    }
    
    // Users collection (existing rule - make sure this exists)
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Admin access (if you have admin functionality)
    match /{document=**} {
      allow read, write: if request.auth != null && 
        request.auth.token.email in [
          '<EMAIL>',  // Replace with your admin email
          '<EMAIL>'  // Replace with your email
        ];
    }
  }
}
