import 'dart:async';
import 'package:flutter_speech/flutter_speech.dart';
import 'package:permission_handler/permission_handler.dart';

class SpeechRecognitionService {
  static final SpeechRecognitionService _instance = SpeechRecognitionService._internal();
  factory SpeechRecognitionService() => _instance;
  SpeechRecognitionService._internal();

  SpeechRecognition? _speech;
  bool _isInitialized = false;
  bool _isListening = false;
  String? _lastResult;
  String? _lastPartialResult;
  Timer? _recognitionTimer;
  Timer? _pauseDetectionTimer;
  String _currentLocale = 'en-US';
  DateTime? _lastSpeechTime;

  final StreamController<String> _resultController = StreamController<String>.broadcast();
  final StreamController<bool> _listeningController = StreamController<bool>.broadcast();
  final StreamController<String> _errorController = StreamController<String>.broadcast();

  Stream<String> get onResult => _resultController.stream;
  Stream<bool> get onListeningChange => _listeningController.stream;
  Stream<String> get onError => _errorController.stream;

  bool get isListening => _isListening;
  bool get isInitialized => _isInitialized;

  Future<bool> initialize() async {
    try {
      if (_isInitialized) return true;

      final permissionStatus = await Permission.microphone.request();
      if (permissionStatus != PermissionStatus.granted) {
        _errorController.add('Microphone permission denied');
        return false;
      }

      _speech = SpeechRecognition();

      _speech!.setAvailabilityHandler((bool result) {
        if (!_isInitialized) {
          _isInitialized = result;
          if (!result) {
            _errorController.add('Speech recognition not available on this device');
          }
        }
      });

      _speech!.setRecognitionStartedHandler(() {
        _isListening = true;
        _listeningController.add(true);
      });

      _speech!.setRecognitionResultHandler((String text) {
        if (text.isNotEmpty) {
          _lastPartialResult = text;
          _lastSpeechTime = DateTime.now();
          _resultController.add(text);

          _pauseDetectionTimer?.cancel();
          _pauseDetectionTimer = Timer(const Duration(seconds: 3), () {
            if (_isListening && _lastPartialResult != null && _lastPartialResult!.isNotEmpty) {

              _finalizeRecognition(_lastPartialResult!);
            }
          });
        }
      });

      _speech!.setRecognitionCompleteHandler((String text) {


        if (_isListening) {
          Timer(const Duration(milliseconds: 100), () {
            if (_isListening) {
              final finalText = _chooseBestResult(text, _lastPartialResult);

              if (finalText.isNotEmpty && finalText.trim().length > 1) {

                _finalizeRecognition(finalText);
              } else {
                _isListening = false;
                _listeningController.add(false);
                _errorController.add('No speech detected. Please speak clearly and try again.');
              }
            }
          });
        }
      });



      final locales = ['en-US', 'en_US', 'en', 'en-GB'];
      bool activated = false;

      for (String locale in locales) {
        try {
          activated = await _speech!.activate(locale);
          if (activated) {
            _currentLocale = locale;
            break;
          }
        } catch (e) {
          continue;
        }
      }

      _isInitialized = activated;

      if (!activated) {
        _errorController.add('Speech recognition activation failed with all locales');
      }

      return activated;
    } catch (e) {
      _errorController.add('Failed to initialize speech recognition: $e');
      return false;
    }
  }

  void _finalizeRecognition(String finalText) {
    _isListening = false;
    _listeningController.add(false);
    _lastResult = finalText;
    _resultController.add("SPEECH_COMPLETED:$finalText");
    _pauseDetectionTimer?.cancel();
  }

  String _chooseBestResult(String finalResult, String? partialResult) {
    if (partialResult == null || partialResult.isEmpty) {
      return finalResult;
    }

    if (finalResult.isEmpty) {
      return partialResult;
    }

    final partialWords = partialResult.trim().split(' ');
    final finalWords = finalResult.trim().split(' ');

    if (finalWords.length > partialWords.length) {
      return finalResult;
    }

    if (partialWords.length > finalWords.length) {
      return partialResult;
    }

    if (finalResult.length > partialResult.length) {
      return finalResult;
    }

    if (partialResult.length > finalResult.length) {
      return partialResult;
    }

    return finalResult;
  }

  Future<bool> startListening({
    Duration? listenFor,
    Duration? pauseFor,
    bool partialResults = true,
  }) async {
    try {
      if (!_isInitialized) {
        final initialized = await initialize();
        if (!initialized) {
          print('Failed to initialize speech recognition');
          return false;
        }
      }

      if (_isListening) {
        await stopListening();
        await Future.delayed(const Duration(milliseconds: 100));
      }

      _lastResult = null;
      _lastPartialResult = null;
      _lastSpeechTime = null;
      _pauseDetectionTimer?.cancel();

      print('Starting speech recognition...');
      final result = await _speech!.listen();

      if (result) {
        print('Speech recognition started successfully');
        _recognitionTimer?.cancel();
        _recognitionTimer = Timer(listenFor ?? const Duration(seconds: 45), () {
          print('Auto-stopping speech recognition after timeout');
          stopListening();
        });
      } else {
        print('Failed to start speech recognition');
      }

      return result;
    } catch (e) {
      print('Error starting speech recognition: $e');
      _errorController.add('Failed to start listening: $e');
      return false;
    }
  }

  Future<String?> stopListening() async {
    try {
      if (_isListening) {
        print('Stopping speech recognition...');
        await _speech!.stop();
        _recognitionTimer?.cancel();
        _pauseDetectionTimer?.cancel();
        _isListening = false;
        _listeningController.add(false);
      }
      return _lastResult;
    } catch (e) {
      print('Error stopping speech recognition: $e');
      _errorController.add('Failed to stop listening: $e');
      return _lastResult;
    }
  }

  Future<bool> isAvailable() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }
      return _isInitialized;
    } catch (e) {
      return false;
    }
  }

  void resetAvailability() {
    if (_speech != null) {
      _isInitialized = true;
    }
  }

  Future<List<String>> getLocales() async {
    try {
      return ['en_US', 'en_GB', 'es_ES', 'fr_FR', 'de_DE', 'it_IT', 'pt_BR', 'ru_RU', 'ja_JP', 'ko_KR', 'zh_CN'];
    } catch (e) {
      return ['en_US'];
    }
  }

  void dispose() {
    try {
      _speech?.stop();
    } catch (e) {
    }
    _recognitionTimer?.cancel();
    _pauseDetectionTimer?.cancel();
    _resultController.close();
    _listeningController.close();
    _errorController.close();
  }
}