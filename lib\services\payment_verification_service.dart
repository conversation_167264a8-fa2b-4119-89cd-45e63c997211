import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class PaymentVerificationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Verify Stripe subscription status
  static Future<void> verifyStripeSubscription() async {
    final user = _auth.currentUser;
    if (user == null) return;

    try {
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (!userDoc.exists) return;

      final userData = userDoc.data()!;
      final stripeCustomerId = userData['payment']?['stripe']?['customerId'];
      final subscriptionId = userData['payment']?['stripe']?['subscriptionId'];

      if (stripeCustomerId == null || subscriptionId == null) return;

      // Call your backend API to verify subscription status
      // This requires a simple backend endpoint that uses Stripe API
      final response = await http.get(
        Uri.parse('YOUR_BACKEND_URL/verify-stripe-subscription/$subscriptionId'),
        headers: {'Authorization': 'Bearer ${await user.getIdToken()}'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        await _updateSubscriptionStatus(user.uid, data);
      }
    } catch (e) {
      print('Error verifying Stripe subscription: $e');
    }
  }

  /// Verify PayPal subscription status
  static Future<void> verifyPayPalSubscription() async {
    final user = _auth.currentUser;
    if (user == null) return;

    try {
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (!userDoc.exists) return;

      final userData = userDoc.data()!;
      final subscriptionId = userData['payment']?['paypal']?['subscriptionId'];

      if (subscriptionId == null) return;

      // Call your backend API to verify subscription status
      final response = await http.get(
        Uri.parse('YOUR_BACKEND_URL/verify-paypal-subscription/$subscriptionId'),
        headers: {'Authorization': 'Bearer ${await user.getIdToken()}'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        await _updateSubscriptionStatus(user.uid, data);
      }
    } catch (e) {
      print('Error verifying PayPal subscription: $e');
    }
  }

  /// Update subscription status in Firestore
  static Future<void> _updateSubscriptionStatus(String userId, Map<String, dynamic> subscriptionData) async {
    await _firestore.collection('users').doc(userId).update({
      'subscription.status': subscriptionData['status'],
      'subscription.endDate': subscriptionData['endDate'] != null 
          ? Timestamp.fromDate(DateTime.parse(subscriptionData['endDate']))
          : null,
      'subscription.autoRenew': subscriptionData['autoRenew'] ?? false,
    });
  }

  /// Call this on app startup
  static Future<void> verifyAllSubscriptions() async {
    await Future.wait([
      verifyStripeSubscription(),
      verifyPayPalSubscription(),
    ]);
  }
}
