import 'dart:async';
import 'package:flight_fear_wellness_app/models/chat_message.dart';
import 'package:flight_fear_wellness_app/services/firebase_service.dart';
import 'package:flight_fear_wellness_app/services/secure_api_service.dart';
import 'package:flight_fear_wellness_app/services/crisis_intervention_service.dart';
import 'package:flight_fear_wellness_app/providers/subscription_provider.dart';


class ChatRepository {
  final FirebaseService firebaseService;
  final SecureAPIService _secureAPIService = SecureAPIService();
  final SubscriptionProvider? subscriptionProvider;

  ChatRepository({
    required this.firebaseService,
    this.subscriptionProvider,
  });

  Stream<List<ChatMessage>> getChatMessages(String userId) {
    return firebaseService.getChatMessages(userId);
  }

  // Purpose: Save chat message to Firebase with error handling
  Future<void> saveMessage(ChatMessage message) async {
    final response = await firebaseService.saveChatMessage(message);
    if (!response.success) {
      throw Exception(response.error ?? 'Failed to save message');
    }
  }

  // Purpose: Generate AI response using secure API with mood analysis and credit validation
  Future<MoodBasedChatResponse> generateAIResponse(
    String userId,
    String message,
    {List<ChatMessage>? conversationHistory}
  ) async {
    try {
      final historyForAPI = conversationHistory?.map((msg) => {
        'role': msg.type == MessageType.user ? 'user' : 'assistant',
        'content': msg.content,
      }).toList() ?? [];

      final response = await _secureAPIService.processChatRequest(
        message: message,
        conversationHistory: historyForAPI,
      );

      if (!response['success']) {
        throw Exception(response['error'] ?? 'Failed to generate AI response');
      }

      final therapeuticResponse = response['message'];
      final moodLevel = response['moodLevel'] ?? 'normal';

      if (subscriptionProvider != null && response['updatedCredits'] != null) {
        print('ChatRepository: Updating subscription provider with new credit data');
        print('ChatRepository: Credit data received: ${response['updatedCredits']}');
        subscriptionProvider!.updateCreditsDirectly(response['updatedCredits']);
      } else {
        print('ChatRepository: Warning - No subscription provider or credit data available');
        if (subscriptionProvider == null) {
          print('ChatRepository: Subscription provider is null');
        }
        if (response['updatedCredits'] == null) {
          print('ChatRepository: Updated credits data is null');
        }
      }

      final aiMessage = ChatMessage(
        id: '${DateTime.now().millisecondsSinceEpoch}_ai',
        userId: userId,
        content: therapeuticResponse,
        type: MessageType.ai,
        timestamp: DateTime.now(),
        metadata: {
          'conversationId': DateTime.now().millisecondsSinceEpoch.toString(),
          'tokensUsed': response['usage']?['total_tokens'] ?? 0,
          'isTherapeutic': true,
          'model': response['model'] ?? 'gpt-4o-mini',
          'moodLevel': moodLevel,
        },
      );

      await saveMessage(aiMessage);

      final interventionOptions = CrisisInterventionService.getCrisisOptions(moodLevel);

      final shouldOfferDebate = CrisisInterventionService.shouldOfferDebate(message, historyForAPI);

      return MoodBasedChatResponse(
        message: therapeuticResponse,
        moodLevel: moodLevel,
        interventionOptions: interventionOptions,
        shouldOfferDebate: shouldOfferDebate,
        metadata: {
          'chatMessage': aiMessage,
          'tokensUsed': response['usage']?['total_tokens'] ?? 0,
          'model': response['model'] ?? 'gpt-4o-mini',
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  // Purpose: Clear all chat history for a specific user
  Future<void> clearChatHistory(String userId) async {
    final response = await firebaseService.deleteChatHistory(userId);
    if (!response.success) {
      throw Exception(response.error ?? 'Failed to delete chat history');
    }
  }

  
}